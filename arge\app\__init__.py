"""
Flask uygulaması başlatıcı
"""
from flask import Flask
from app.controllers.main_controller import MainController
from app.models.config import Config

def create_app():
    """
    Flask uygulamasını oluşturur
    
    Returns:
        Flask: Flask uygulaması
    """
    app = Flask(__name__,
                template_folder='templates',
                static_folder='static')
    app.config.from_object(Config)
    
    controller = MainController()
    
    # Route'ları tanımla
    app.add_url_rule('/', 'index', controller.index)
    app.add_url_rule('/api/market-data', 'get_market_data', controller.get_market_data)
    app.add_url_rule('/api/exchange-info', 'get_exchange_info', controller.get_exchange_info)
    app.add_url_rule('/api/run-simulation', 'run_simulation', controller.run_simulation, methods=['POST'])
    app.add_url_rule('/api/get-performance', 'get_performance', controller.get_performance)
    app.add_url_rule('/api/get-trade-history', 'get_trade_history', controller.get_trade_history)
    
    return app 
"""
Simülasyon motoru sınıfı
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from datetime import datetime, timedelta
from .strategy import TradingStrategy
from .indicators import TechnicalIndicators

class SimulationEngine:
    def __init__(self, config):
        """
        SimulationEngine sınıfının başlatıcı metodu
        
        Args:
            config: Konfigürasyon nesnesi
        """
        self.config = config
        self.strategy = TradingStrategy(config)
        self.indicators = TechnicalIndicators()
        
    def run_simulation(self, df: pd.DataFrame, initial_balance: float = 10000) -> Dict:
        """
        Simülasyonu çalıştırır
        
        Args:
            df (pd.DataFrame): Fiyat verileri
            initial_balance (float): Başlangıç bakiyesi
            
        Returns:
            Dict: Simülasyon sonuçları
        """
        # İndikatörleri hesapla
        df = self.indicators.add_all_indicators(df)
        
        # Sinyalleri üret
        df = self.strategy.generate_signals(df)
        
        # Backtest yap
        backtest_results = self.strategy.backtest(df, initial_balance)
        
        # Performans metriklerini hesapla
        df['returns'] = df['close'].pct_change()
        df['strategy_returns'] = df['signal'].shift(1) * df['returns']
        
        # Yıllık getiri
        annual_return = df['strategy_returns'].mean() * 252
        
        # Volatilite
        volatility = df['strategy_returns'].std() * np.sqrt(252)
        
        # Sharpe oranı
        risk_free_rate = 0.02  # Varsayılan risksiz faiz oranı
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility != 0 else 0
        
        # Maximum drawdown
        df['cumulative_returns'] = (1 + df['strategy_returns']).cumprod()
        df['rolling_max'] = df['cumulative_returns'].expanding().max()
        df['drawdown'] = df['cumulative_returns'] / df['rolling_max'] - 1
        max_drawdown = df['drawdown'].min()
        
        # NaN değerleri None ile değiştir
        equity_curve = df['cumulative_returns'].replace({np.nan: None}).tolist()
        drawdown_curve = df['drawdown'].replace({np.nan: None}).tolist()
        
        # Sonuçları birleştir
        results = {
            **backtest_results,
            'annual_return': float(annual_return) if not np.isnan(annual_return) else 0.0,
            'volatility': float(volatility) if not np.isnan(volatility) else 0.0,
            'sharpe_ratio': float(sharpe_ratio) if not np.isnan(sharpe_ratio) else 0.0,
            'max_drawdown': float(max_drawdown) if not np.isnan(max_drawdown) else 0.0,
            'equity_curve': equity_curve,
            'drawdown_curve': drawdown_curve,
            'dates': df.index.strftime('%Y-%m-%d').tolist()
        }
        
        return results
    
    def get_trade_history(self, results: Dict) -> List[Dict]:
        """
        İşlem geçmişini döndürür
        
        Args:
            results (Dict): Simülasyon sonuçları
            
        Returns:
            List[Dict]: İşlem geçmişi
        """
        trade_history = []
        
        for trade in results['trades']:
            trade_info = {
                'type': trade['type'],
                'price': float(trade['price']) if not np.isnan(trade['price']) else 0.0,
                'balance': float(trade['balance']) if not np.isnan(trade['balance']) else 0.0
            }
            
            if 'profit' in trade:
                trade_info['profit'] = float(trade['profit']) if not np.isnan(trade['profit']) else 0.0
            
            if 'size' in trade:
                trade_info['size'] = float(trade['size']) if not np.isnan(trade['size']) else 0.0
            
            trade_history.append(trade_info)
        
        return trade_history
    
    def get_performance_metrics(self, results: Dict) -> Dict:
        """
        Performans metriklerini döndürür
        
        Args:
            results (Dict): Simülasyon sonuçları
            
        Returns:
            Dict: Performans metrikleri
        """
        return {
            'initial_balance': float(results['initial_balance']),
            'final_balance': float(results['final_balance']),
            'total_profit': float(results['total_profit']),
            'profit_percentage': float(results['profit_percentage']),
            'total_trades': int(results['total_trades']),
            'winning_trades': int(results['winning_trades']),
            'losing_trades': int(results['losing_trades']),
            'win_rate': float(results['win_rate']),
            'annual_return': float(results['annual_return']),
            'volatility': float(results['volatility']),
            'sharpe_ratio': float(results['sharpe_ratio']),
            'max_drawdown': float(results['max_drawdown'])
        } 
// Grafik nesnesi
let priceChart = null;

// Sayfa yüklendiğinde
document.addEventListener('DOMContentLoaded', function() {
    // Form submit olayını dinle
    document.getElementById('marketDataForm').addEventListener('submit', function(e) {
        e.preventDefault();
        fetchMarketData();
    });
    
    // İlk veriyi yükle
    fetchMarketData();
});

// Piyasa verilerini getir
function fetchMarketData() {
    const symbol = document.getElementById('symbol').value;
    const interval = document.getElementById('interval').value;
    
    fetch(`/api/market-data?symbol=${symbol}&interval=${interval}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert(data.error);
                return;
            }
            updateChart(data);
        })
        .catch(error => {
            console.error('Veri çekme hatası:', error);
            alert('Veriler alınamadı!');
        });
}

// Grafiği güncelle
function updateChart(data) {
    const ctx = document.getElementById('priceChart').getContext('2d');
    
    // Eğer grafik zaten varsa, yok et
    if (priceChart) {
        priceChart.destroy();
    }
    
    // Yeni grafik oluştur
    priceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.data.map(item => new Date(item.timestamp).toLocaleString()),
            datasets: [{
                label: `${data.symbol} Fiyat`,
                data: data.data.map(item => item.close),
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: `${data.symbol} ${data.interval} Grafiği`
                }
            },
            scales: {
                y: {
                    beginAtZero: false
                }
            }
        }
    });
} 
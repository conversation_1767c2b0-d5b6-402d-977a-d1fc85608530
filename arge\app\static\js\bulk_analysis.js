// Toplu analiz JavaScript dosyası

let analysisResults = [];
let isAnalysisRunning = false;
let optimizationHistory = [];
let currentIteration = 0;
let isOptimizationRunning = false;
let debugLog = [];

// <PERSON>fa yüklendiğinde çalışacak fonksiyonlar
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    setupEventListeners();
});

// Sayfayı başlat
function initializePage() {
    // Başlangıç tarihini ayarla (1 hafta önce)
    const startDate = document.getElementById('bulkStartDate');
    const today = new Date();
    const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    startDate.value = oneWeekAgo.toISOString().split('T')[0];
    
    // Bootstrap tooltip'lerini başlat
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Event listener'ları ayarla
function setupEventListeners() {
    // Form submit
    document.getElementById('bulkAnalysisForm').addEventListener('submit', function(e) {
        e.preventDefault();
        startBulkAnalysis();
    });
    
    // Symbol filter değişikliği
    document.getElementById('symbolFilter').addEventListener('change', function() {
        const customDiv = document.getElementById('customSymbolsDiv');
        if (this.value === 'custom') {
            customDiv.style.display = 'block';
        } else {
            customDiv.style.display = 'none';
        }
    });
    
    // Export results
    document.getElementById('exportResults').addEventListener('click', exportResults);
    
    // Generate AI strategy
    document.getElementById('generateAIStrategy').addEventListener('click', generateAIStrategy);
    
    // Apply AI strategy
    document.getElementById('applyAIStrategy').addEventListener('click', applyAIStrategy);

    // Load test data
    document.getElementById('loadTestData').addEventListener('click', loadTestData);

    // Cyclic optimization
    document.getElementById('startCyclicOptimization').addEventListener('click', startCyclicOptimization);

    // Döngüsel optimizasyon checkbox
    document.getElementById('enableCyclicOptimization').addEventListener('change', function() {
        const cyclicBtn = document.getElementById('startCyclicOptimization');
        if (this.checked) {
            cyclicBtn.style.display = 'block';
        } else {
            cyclicBtn.style.display = 'none';
        }
    });
}

// Toplu analizi başlat
async function startBulkAnalysis() {
    if (isAnalysisRunning) {
        alert('Analiz zaten çalışıyor!');
        return;
    }
    
    isAnalysisRunning = true;
    analysisResults = [];
    
    try {
        // Form verilerini al
        const timeframe = document.getElementById('bulkTimeframe').value;
        const startDate = document.getElementById('bulkStartDate').value;
        const initialBalance = parseFloat(document.getElementById('bulkInitialBalance').value);
        const symbolFilter = document.getElementById('symbolFilter').value;
        const useCurrentStrategy = document.getElementById('useCurrentStrategy').checked;
        
        // Sembol listesini al
        const symbols = await getSymbolList(symbolFilter);
        
        if (symbols.length === 0) {
            alert('Analiz edilecek para birimi bulunamadı!');
            return;
        }
        
        // Progress card'ı göster
        showProgressCard(symbols.length);
        
        // Her sembol için analiz çalıştır
        for (let i = 0; i < symbols.length; i++) {
            const symbol = symbols[i];
            updateProgress(i + 1, symbols.length, symbol);
            
            try {
                const result = await runSingleAnalysis(symbol, timeframe, startDate, initialBalance, useCurrentStrategy);
                result.symbol = symbol;
                result.index = i + 1;
                analysisResults.push(result);
            } catch (error) {
                console.error(`${symbol} analizi başarısız:`, error);
                analysisResults.push({
                    symbol: symbol,
                    index: i + 1,
                    error: error.message,
                    total_profit: 0,
                    profit_percentage: 0,
                    total_trades: 0,
                    win_rate: 0,
                    sharpe_ratio: 0,
                    max_drawdown: 0
                });
            }
            
            // Kısa bir bekleme (API rate limiting için)
            await sleep(100);
        }
        
        // Sonuçları göster
        displayResults();
        hideProgressCard();
        
    } catch (error) {
        console.error('Toplu analiz hatası:', error);
        alert('Toplu analiz sırasında hata oluştu: ' + error.message);
    } finally {
        isAnalysisRunning = false;
    }
}

// Sembol listesini al
async function getSymbolList(filter) {
    const predefinedSymbols = {
        'major': ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT'],
        'top20': [
            'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT',
            'XRP/USDT', 'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'SHIB/USDT',
            'MATIC/USDT', 'LTC/USDT', 'UNI/USDT', 'LINK/USDT', 'ATOM/USDT',
            'ETC/USDT', 'XLM/USDT', 'BCH/USDT', 'ALGO/USDT', 'VET/USDT'
        ]
    };
    
    if (filter === 'custom') {
        const customSymbols = document.getElementById('customSymbols').value;
        return customSymbols.split(',').map(s => s.trim()).filter(s => s.length > 0);
    } else if (predefinedSymbols[filter]) {
        return predefinedSymbols[filter];
    } else if (filter === 'all') {
        // Tüm semboller için API'den al
        try {
            const response = await fetch('/api/exchange-info');
            const data = await response.json();
            return data.symbols.slice(0, 50); // İlk 50 sembol
        } catch (error) {
            console.error('Sembol listesi alınamadı:', error);
            return predefinedSymbols['major']; // Fallback
        }
    }
    
    return [];
}

// Tek bir sembol için analiz çalıştır
async function runSingleAnalysis(symbol, timeframe, startDate, initialBalance, useCurrentStrategy) {
    // Mevcut strateji ayarlarını al
    let strategySettings = null;
    if (useCurrentStrategy) {
        try {
            const strategyResponse = await fetch('/api/get-strategy');
            if (strategyResponse.ok) {
                strategySettings = await strategyResponse.json();
            }
        } catch (error) {
            console.warn('Strateji ayarları alınamadı:', error);
        }
    }

    const requestData = {
        symbol: symbol,
        timeframe: timeframe,
        start_date: startDate,
        initial_balance: initialBalance,
        strategy_settings: strategySettings  // Strateji ayarlarını ekle
    };

    addDebugLog(`${symbol} analizi: ${JSON.stringify(strategySettings ? 'Özel strateji' : 'Varsayılan strateji')}`);

    const response = await fetch('/api/run-simulation', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    });

    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
}

// Progress card'ı göster
function showProgressCard(totalSymbols) {
    document.getElementById('progressCard').style.display = 'block';
    document.getElementById('startBulkAnalysis').disabled = true;
    document.getElementById('startBulkAnalysis').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analiz Ediliyor...';
}

// Progress'i güncelle
function updateProgress(current, total, symbol) {
    const percentage = Math.round((current / total) * 100);
    document.getElementById('progressBar').style.width = percentage + '%';
    document.getElementById('progressBar').textContent = percentage + '%';
    document.getElementById('progressText').textContent = `${current}/${total} tamamlandı`;
    document.getElementById('currentSymbol').textContent = `Şu an analiz ediliyor: ${symbol}`;
}

// Progress card'ı gizle
function hideProgressCard() {
    document.getElementById('progressCard').style.display = 'none';
    document.getElementById('startBulkAnalysis').disabled = false;
    document.getElementById('startBulkAnalysis').innerHTML = '<i class="fas fa-play"></i> Toplu Analizi Başlat';
}

// Sonuçları göster
function displayResults() {
    // Sonuçları kar/zarar oranına göre sırala
    analysisResults.sort((a, b) => (b.profit_percentage || 0) - (a.profit_percentage || 0));
    
    // Özet istatistikleri göster
    displaySummaryStats();
    
    // Detaylı tabloyu göster
    displayDetailedTable();
    
    // Export ve AI butonlarını göster
    document.getElementById('exportResults').style.display = 'inline-block';
    document.getElementById('generateAIStrategy').style.display = 'inline-block';
    
    // Sonuç alanlarını göster
    document.getElementById('detailedResultsRow').style.display = 'block';
    document.getElementById('summaryStatsRow').style.display = 'block';
    
    // Ana sonuç alanını güncelle
    updateMainResultsArea();
}

// Özet istatistikleri göster
function displaySummaryStats() {
    const validResults = analysisResults.filter(r => !r.error);
    
    if (validResults.length === 0) return;
    
    // En iyi ve en kötü performans
    const best = validResults[0];
    const worst = validResults[validResults.length - 1];
    
    document.getElementById('bestPerformance').textContent = `+${best.profit_percentage.toFixed(2)}%`;
    document.getElementById('bestSymbol').textContent = best.symbol;
    
    document.getElementById('worstPerformance').textContent = `${worst.profit_percentage.toFixed(2)}%`;
    document.getElementById('worstSymbol').textContent = worst.symbol;
    
    // Ortalama kazanma oranı
    const avgWinRate = validResults.reduce((sum, r) => sum + (r.win_rate || 0), 0) / validResults.length;
    document.getElementById('avgWinRate').textContent = `${(avgWinRate * 100).toFixed(1)}%`;
    
    // Karlı para birimi sayısı
    const profitableCount = validResults.filter(r => r.profit_percentage > 0).length;
    const profitablePercentage = (profitableCount / validResults.length) * 100;
    
    document.getElementById('profitableCount').textContent = `${profitableCount}/${validResults.length}`;
    document.getElementById('profitablePercentage').textContent = `${profitablePercentage.toFixed(1)}% karlı`;
}

// Detaylı tabloyu göster
function displayDetailedTable() {
    const tbody = document.querySelector('#detailedResultsTable tbody');
    tbody.innerHTML = '';
    
    analysisResults.forEach((result, index) => {
        const row = document.createElement('tr');
        
        // Durum belirleme
        let status, statusClass;
        if (result.error) {
            status = 'Hata';
            statusClass = 'text-danger';
        } else if (result.profit_percentage > 5) {
            status = 'Mükemmel';
            statusClass = 'text-success fw-bold';
        } else if (result.profit_percentage > 0) {
            status = 'Karlı';
            statusClass = 'text-success';
        } else if (result.profit_percentage > -5) {
            status = 'Nötr';
            statusClass = 'text-warning';
        } else {
            status = 'Zararlı';
            statusClass = 'text-danger';
        }
        
        row.innerHTML = `
            <td>${index + 1}</td>
            <td><strong>${result.symbol}</strong></td>
            <td class="${result.profit_percentage >= 0 ? 'text-success' : 'text-danger'}">
                ${result.error ? 'N/A' : result.total_profit.toFixed(2)} USDT
            </td>
            <td class="${result.profit_percentage >= 0 ? 'text-success' : 'text-danger'}">
                ${result.error ? 'N/A' : result.profit_percentage.toFixed(2)}%
            </td>
            <td>${result.error ? 'N/A' : result.total_trades}</td>
            <td>${result.error ? 'N/A' : (result.win_rate * 100).toFixed(1)}%</td>
            <td>${result.error ? 'N/A' : result.sharpe_ratio.toFixed(2)}</td>
            <td class="text-danger">${result.error ? 'N/A' : (result.max_drawdown * 100).toFixed(2)}%</td>
            <td class="${statusClass}">${status}</td>
        `;
        
        tbody.appendChild(row);
    });
}

// Ana sonuç alanını güncelle
function updateMainResultsArea() {
    const validResults = analysisResults.filter(r => !r.error);
    const totalSymbols = analysisResults.length;
    const successfulAnalyses = validResults.length;
    
    const resultsDiv = document.getElementById('analysisResults');
    resultsDiv.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h5>📊 Analiz Özeti</h5>
                <ul class="list-unstyled">
                    <li><strong>Toplam Analiz:</strong> ${totalSymbols}</li>
                    <li><strong>Başarılı:</strong> ${successfulAnalyses}</li>
                    <li><strong>Hatalı:</strong> ${totalSymbols - successfulAnalyses}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h5>🎯 Performans Dağılımı</h5>
                <div class="mb-2">
                    <span class="badge bg-success">Mükemmel (>5%): ${validResults.filter(r => r.profit_percentage > 5).length}</span>
                </div>
                <div class="mb-2">
                    <span class="badge bg-primary">Karlı (0-5%): ${validResults.filter(r => r.profit_percentage > 0 && r.profit_percentage <= 5).length}</span>
                </div>
                <div class="mb-2">
                    <span class="badge bg-warning">Nötr (-5-0%): ${validResults.filter(r => r.profit_percentage >= -5 && r.profit_percentage <= 0).length}</span>
                </div>
                <div class="mb-2">
                    <span class="badge bg-danger">Zararlı (<-5%): ${validResults.filter(r => r.profit_percentage < -5).length}</span>
                </div>
            </div>
        </div>
    `;
}

// Sonuçları dışa aktar
function exportResults() {
    const csvContent = generateCSV();
    downloadCSV(csvContent, 'bulk_analysis_results.csv');
}

// CSV oluştur
function generateCSV() {
    const headers = ['Sıra', 'Para Birimi', 'Toplam Kar/Zarar', 'Kar %', 'İşlem Sayısı', 'Kazanma Oranı', 'Sharpe Oranı', 'Max Drawdown', 'Durum'];
    let csv = headers.join(',') + '\n';
    
    analysisResults.forEach((result, index) => {
        const row = [
            index + 1,
            result.symbol,
            result.error ? 'N/A' : result.total_profit.toFixed(2),
            result.error ? 'N/A' : result.profit_percentage.toFixed(2),
            result.error ? 'N/A' : result.total_trades,
            result.error ? 'N/A' : (result.win_rate * 100).toFixed(1),
            result.error ? 'N/A' : result.sharpe_ratio.toFixed(2),
            result.error ? 'N/A' : (result.max_drawdown * 100).toFixed(2),
            result.error ? 'Hata' : (result.profit_percentage > 0 ? 'Karlı' : 'Zararlı')
        ];
        csv += row.join(',') + '\n';
    });
    
    return csv;
}

// CSV dosyasını indir
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// AI strateji oluştur
async function generateAIStrategy() {
    const modal = new bootstrap.Modal(document.getElementById('aiStrategyModal'));
    modal.show();
    
    try {
        // AI analiz sonuçlarını gönder
        const response = await fetch('/api/generate-ai-strategy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                analysis_results: analysisResults
            })
        });
        
        if (!response.ok) {
            throw new Error('AI strateji oluşturulamadı');
        }
        
        const aiStrategy = await response.json();
        displayAIStrategy(aiStrategy);
        
    } catch (error) {
        console.error('AI strateji hatası:', error);
        document.getElementById('aiStrategyContent').innerHTML = `
            <div class="alert alert-danger">
                <h6>Hata!</h6>
                <p>AI strateji oluşturulurken hata oluştu: ${error.message}</p>
            </div>
        `;
    }
}

// AI stratejisini göster
function displayAIStrategy(strategy) {
    document.getElementById('aiStrategyContent').innerHTML = `
        <div class="alert alert-info">
            <h6><i class="fas fa-robot"></i> AI Analiz Sonucu</h6>
            <p>${strategy.analysis}</p>
        </div>
        
        <h6>Önerilen Strateji Parametreleri:</h6>
        <div class="row">
            <div class="col-md-6">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between">
                        <span>Stop Loss:</span>
                        <strong>${strategy.parameters.stopLoss}%</strong>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>Take Profit:</span>
                        <strong>${strategy.parameters.takeProfit}%</strong>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>Pozisyon Büyüklüğü:</span>
                        <strong>${strategy.parameters.basePosition}%</strong>
                    </li>
                </ul>
            </div>
            <div class="col-md-6">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between">
                        <span>RSI Alt Sınır:</span>
                        <strong>${strategy.parameters.rsiLower}</strong>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>RSI Üst Sınır:</span>
                        <strong>${strategy.parameters.rsiUpper}</strong>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>Momentum Skoru:</span>
                        <strong>${strategy.parameters.momentumScore}/6</strong>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="mt-3">
            <h6>Beklenen Performans:</h6>
            <div class="row">
                <div class="col-md-4 text-center">
                    <div class="border rounded p-2">
                        <small class="text-muted">Kazanma Oranı</small>
                        <div class="h5 text-success">${strategy.expected.winRate}%</div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="border rounded p-2">
                        <small class="text-muted">Ortalama Kar</small>
                        <div class="h5 text-primary">${strategy.expected.avgProfit}%</div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="border rounded p-2">
                        <small class="text-muted">Risk Seviyesi</small>
                        <div class="h5 text-warning">${strategy.expected.riskLevel}</div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Apply butonunu göster
    document.getElementById('applyAIStrategy').style.display = 'inline-block';
    
    // Stratejiyi sakla
    window.currentAIStrategy = strategy.parameters;
}

// AI stratejisini uygula
function applyAIStrategy() {
    if (window.currentAIStrategy) {
        // Strateji sayfasına yönlendir ve parametreleri uygula
        localStorage.setItem('aiGeneratedStrategy', JSON.stringify(window.currentAIStrategy));
        window.location.href = '/strategy?ai=true';
    }
}

// Test verisi yükle (bulk_analysis_results.csv'den)
function loadTestData() {
    // Mevcut CSV verilerini simüle et (gelişmiş indikatörler öncesi sonuçlar)
    const testResults = [
        {
            symbol: 'ADA/USDT',
            index: 1,
            total_profit: 0.00,
            profit_percentage: 0.00,
            total_trades: 0,  // Hiç işlem yok - çok seçici
            win_rate: 0.0,
            sharpe_ratio: 0.42,
            max_drawdown: 0.0878
        },
        {
            symbol: 'SOL/USDT',
            index: 2,
            total_profit: 0.00,
            profit_percentage: 0.00,
            total_trades: 0,  // Hiç işlem yok - çok seçici
            win_rate: 0.0,
            sharpe_ratio: 0.78,
            max_drawdown: 0.0750
        },
        {
            symbol: 'ETH/USDT',
            index: 3,
            total_profit: -11.41,
            profit_percentage: -0.11,
            total_trades: 2,  // Çok az işlem
            win_rate: 0.5,
            sharpe_ratio: 0.19,
            max_drawdown: 0.0735
        },
        {
            symbol: 'BNB/USDT',
            index: 4,
            total_profit: -24.87,
            profit_percentage: -0.25,
            total_trades: 2,  // Çok az işlem
            win_rate: 0.5,
            sharpe_ratio: 0.54,
            max_drawdown: 0.0376
        },
        {
            symbol: 'BTC/USDT',
            index: 5,
            total_profit: -56.39,
            profit_percentage: -0.56,
            total_trades: 2,  // Çok az işlem, kötü performans
            win_rate: 0.0,
            sharpe_ratio: -0.64,
            max_drawdown: 0.0530
        }
    ];

    // Sonuçları yükle
    analysisResults = testResults;

    // Sonuçları göster
    displayResults();

    // Başarı mesajı
    showNotification('Test verileri başarıyla yüklendi! Şimdi AI strateji oluşturabilirsiniz.', 'success');
}

// Bildirim göster
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 5 saniye sonra otomatik kapat
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// Debug log fonksiyonu
function addDebugLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    debugLog.push(`[${timestamp}] ${message}`);

    // Debug log'u güncelle
    const debugElement = document.getElementById('debugLog');
    if (debugElement) {
        debugElement.textContent = debugLog.slice(-20).join('\n'); // Son 20 log
        debugElement.scrollTop = debugElement.scrollHeight;
    }

    console.log(`[CYCLIC DEBUG] ${message}`);
}

// Döngüsel progress göster
function showCyclicProgress(iteration, maxIterations, targetProfit, currentProfit, step) {
    document.getElementById('cyclicProgressCard').style.display = 'block';
    document.getElementById('currentIterationDisplay').textContent = `İterasyon ${iteration}`;
    document.getElementById('targetProfitDisplay').textContent = `Hedef: %${targetProfit}`;
    document.getElementById('currentProfitDisplay').textContent = `Mevcut: %${currentProfit.toFixed(2)}`;

    const progress = (iteration / maxIterations) * 100;
    document.getElementById('cyclicProgressBar').style.width = progress + '%';
    document.getElementById('cyclicProgressText').textContent = `İterasyon ${iteration}/${maxIterations} - ${step}`;

    addDebugLog(`İterasyon ${iteration}: ${step}`);
}

// Döngüsel progress gizle
function hideCyclicProgress() {
    document.getElementById('cyclicProgressCard').style.display = 'none';
}

// Döngüsel optimizasyonu başlat
async function startCyclicOptimization() {
    if (isOptimizationRunning) {
        alert('Optimizasyon zaten çalışıyor!');
        return;
    }

    isOptimizationRunning = true;
    optimizationHistory = [];
    currentIteration = 0;
    debugLog = [];

    addDebugLog('Döngüsel optimizasyon başlatılıyor...');

    try {
        // Hedef ve ayarları al
        const targetProfit = parseFloat(document.getElementById('targetProfit').value);
        const maxIterations = parseInt(document.getElementById('maxIterations').value);
        const minImprovement = parseFloat(document.getElementById('minImprovement').value);

        addDebugLog(`Hedef kar: %${targetProfit}, Max iterasyon: ${maxIterations}, Min iyileştirme: %${minImprovement}`);

        // Form verilerini al
        const timeframe = document.getElementById('bulkTimeframe').value;
        const startDate = document.getElementById('bulkStartDate').value;
        const initialBalance = parseFloat(document.getElementById('bulkInitialBalance').value);
        const symbolFilter = document.getElementById('symbolFilter').value;

        addDebugLog(`Analiz ayarları: ${timeframe}, ${startDate}, ${initialBalance} USDT, ${symbolFilter}`);

        // Sembol listesini al
        const symbols = await getSymbolList(symbolFilter);

        if (symbols.length === 0) {
            addDebugLog('HATA: Analiz edilecek para birimi bulunamadı!');
            alert('Analiz edilecek para birimi bulunamadı!');
            return;
        }

        addDebugLog(`${symbols.length} para birimi analiz edilecek: ${symbols.join(', ')}`);

        showNotification('🔄 Döngüsel AI optimizasyon başlatıldı!', 'info');
        document.getElementById('optimizationHistoryRow').style.display = 'block';

        let bestResult = null;
        let lastAverageProfit = -Infinity;

        for (let iteration = 1; iteration <= maxIterations; iteration++) {
            currentIteration = iteration;

            addDebugLog(`=== İTERASYON ${iteration} BAŞLADI ===`);
            showCyclicProgress(iteration, maxIterations, targetProfit, lastAverageProfit === -Infinity ? 0 : lastAverageProfit, 'Analiz çalıştırılıyor...');
            showNotification(`🔄 İterasyon ${iteration}/${maxIterations} başlatılıyor...`, 'info');

            // Mevcut strateji ile analiz çalıştır
            addDebugLog(`İterasyon ${iteration}: Analiz başlatılıyor...`);
            const currentResults = await runIterationAnalysis(symbols, timeframe, startDate, initialBalance, iteration === 1);
            addDebugLog(`İterasyon ${iteration}: Analiz tamamlandı. ${currentResults.length} sonuç alındı.`);

            // Sonuçları değerlendir
            const iterationStats = calculateIterationStats(currentResults);
            addDebugLog(`İterasyon ${iteration}: Ortalama kar %${iterationStats.averageProfit.toFixed(2)}, Karlı: ${iterationStats.profitableCount}/${iterationStats.totalSymbols}`);

            // Progress güncelle
            showCyclicProgress(iteration, maxIterations, targetProfit, iterationStats.averageProfit, 'Sonuçlar değerlendiriliyor...');

            // Geçmişe ekle
            optimizationHistory.push({
                iteration: iteration,
                results: currentResults,
                stats: iterationStats,
                timestamp: new Date()
            });

            // Grafik ve tabloyu güncelle
            updateOptimizationHistory();

            // Hedef kar kontrolü
            if (iterationStats.averageProfit >= targetProfit) {
                addDebugLog(`🎯 HEDEF KARA ULAŞILDI! Mevcut: %${iterationStats.averageProfit.toFixed(2)}, Hedef: %${targetProfit}`);
                showNotification(`🎯 Hedef kar oranına ulaşıldı! (${iterationStats.averageProfit.toFixed(2)}%)`, 'success');
                break;
            }

            // İyileştirme kontrolü (daha esnek)
            if (iteration > 1) {
                const improvement = iterationStats.averageProfit - lastAverageProfit;
                addDebugLog(`İterasyon ${iteration}: İyileştirme %${improvement.toFixed(2)} (Min: %${minImprovement})`);

                // Sadece 3. iterasyondan sonra iyileştirme kontrolü yap
                if (iteration > 2 && improvement < minImprovement) {
                    addDebugLog(`⚠️ YETERSİZ İYİLEŞTİRME! Optimizasyon durduruluyor.`);
                    showNotification(`⚠️ Yeterli iyileştirme sağlanamadı. Optimizasyon durduruluyor.`, 'warning');
                    break;
                } else if (iteration <= 2) {
                    addDebugLog(`İlk iterasyonlar, iyileştirme kontrolü atlanıyor.`);
                }
            }

            // Son iterasyon değilse AI strateji oluştur ve uygula
            if (iteration < maxIterations) {
                showCyclicProgress(iteration, maxIterations, targetProfit, iterationStats.averageProfit, 'AI strateji oluşturuluyor...');
                addDebugLog(`İterasyon ${iteration}: AI strateji oluşturuluyor...`);

                try {
                    await generateAndApplyAIStrategy(currentResults, iteration);
                    addDebugLog(`İterasyon ${iteration}: AI strateji başarıyla uygulandı, sonraki iterasyona geçiliyor...`);
                } catch (aiError) {
                    addDebugLog(`İterasyon ${iteration}: AI strateji HATASI: ${aiError.message}`);
                    addDebugLog(`İterasyon ${iteration}: Varsayılan strateji ile devam ediliyor...`);
                    showNotification(`⚠️ AI strateji hatası, varsayılan ayarlarla devam ediliyor`, 'warning');
                    // Hata olsa bile devam et
                }

                await sleep(2000); // Biraz daha uzun bekleme
            }

            lastAverageProfit = iterationStats.averageProfit;
            bestResult = iterationStats;

            addDebugLog(`=== İTERASYON ${iteration} TAMAMLANDI ===`);
            addDebugLog(`Sonraki iterasyon: ${iteration + 1}, Max: ${maxIterations}, Devam edecek mi: ${iteration < maxIterations}`);
        }

        // Final sonuçları göster
        addDebugLog('=== DÖNGÜSEL OPTİMİZASYON TAMAMLANDI ===');
        hideCyclicProgress();
        showFinalOptimizationResults(bestResult);

    } catch (error) {
        addDebugLog(`HATA: ${error.message}`);
        console.error('Döngüsel optimizasyon hatası:', error);
        showNotification('Döngüsel optimizasyon sırasında hata oluştu: ' + error.message, 'error');
        hideCyclicProgress();
    } finally {
        isOptimizationRunning = false;
        addDebugLog('Döngüsel optimizasyon sona erdi.');
    }
}

// Tek iterasyon analizi çalıştır
async function runIterationAnalysis(symbols, timeframe, startDate, initialBalance, isFirstIteration) {
    const results = [];

    for (let i = 0; i < symbols.length; i++) {
        const symbol = symbols[i];

        try {
            const result = await runSingleAnalysis(symbol, timeframe, startDate, initialBalance, true);
            result.symbol = symbol;
            result.index = i + 1;
            results.push(result);
        } catch (error) {
            console.error(`${symbol} analizi başarısız:`, error);
            results.push({
                symbol: symbol,
                index: i + 1,
                error: error.message,
                total_profit: 0,
                profit_percentage: 0,
                total_trades: 0,
                win_rate: 0,
                sharpe_ratio: 0,
                max_drawdown: 0
            });
        }

        await sleep(100);
    }

    return results;
}

// İterasyon istatistiklerini hesapla
function calculateIterationStats(results) {
    const validResults = results.filter(r => !r.error);

    if (validResults.length === 0) {
        return {
            averageProfit: 0,
            profitableCount: 0,
            totalSymbols: results.length,
            averageWinRate: 0,
            averageTrades: 0,
            profitabilityRate: 0
        };
    }

    const totalProfit = validResults.reduce((sum, r) => sum + (r.profit_percentage || 0), 0);
    const averageProfit = totalProfit / validResults.length;
    const profitableCount = validResults.filter(r => r.profit_percentage > 0).length;
    const averageWinRate = validResults.reduce((sum, r) => sum + (r.win_rate || 0), 0) / validResults.length;
    const averageTrades = validResults.reduce((sum, r) => sum + (r.total_trades || 0), 0) / validResults.length;
    const profitabilityRate = profitableCount / validResults.length;

    return {
        averageProfit,
        profitableCount,
        totalSymbols: validResults.length,
        averageWinRate,
        averageTrades,
        profitabilityRate
    };
}

// AI strateji oluştur ve uygula
async function generateAndApplyAIStrategy(results, iteration) {
    try {
        addDebugLog(`AI strateji oluşturma başlatıldı (İterasyon ${iteration})`);
        showNotification(`🤖 İterasyon ${iteration} için AI strateji oluşturuluyor...`, 'info');

        // AI strateji oluştur
        addDebugLog('API çağrısı yapılıyor: /api/generate-ai-strategy');
        const response = await fetch('/api/generate-ai-strategy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                analysis_results: results,
                iteration: iteration,
                optimization_history: optimizationHistory
            })
        });

        addDebugLog(`API yanıtı alındı: ${response.status} ${response.statusText}`);

        if (!response.ok) {
            const errorText = await response.text();
            addDebugLog(`API HATASI: ${response.status} - ${errorText}`);
            throw new Error(`AI strateji oluşturulamadı: ${response.status} ${errorText}`);
        }

        const aiStrategy = await response.json();

        // Strateji parametrelerini detaylı logla
        const params = aiStrategy.parameters;
        addDebugLog(`AI strateji alındı (İterasyon ${iteration}):`);
        addDebugLog(`- Momentum Score: ${params.momentumScore}`);
        addDebugLog(`- ADX Minimum: ${params.adxMinimum}`);
        addDebugLog(`- Volatilite Max: ${params.volatilityMax}`);
        addDebugLog(`- RSI Aralığı: ${params.rsiLower}-${params.rsiUpper}`);
        addDebugLog(`- Stop Loss: ${params.stopLoss}%, Take Profit: ${params.takeProfit}%`);
        addDebugLog(`- Base Position: ${params.basePosition}%`);
        addDebugLog(`- VWAP: ${params.useVWAP}, Ichimoku: ${params.useIchimoku}`);
        addDebugLog(`- Komisyon: ${params.commission}%`);

        // Stratejiyi kaydet (session'a)
        addDebugLog('Strateji kaydediliyor...');
        const saveResponse = await fetch('/api/save-strategy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(aiStrategy.parameters)
        });

        if (!saveResponse.ok) {
            addDebugLog(`Strateji kaydetme HATASI: ${saveResponse.status}`);
            throw new Error('Strateji kaydedilemedi');
        }

        addDebugLog(`✅ AI strateji başarıyla uygulandı (İterasyon ${iteration})`);
        showNotification(`✅ İterasyon ${iteration} AI stratejisi uygulandı`, 'success');

    } catch (error) {
        addDebugLog(`AI strateji HATASI: ${error.message}`);
        console.error('AI strateji uygulama hatası:', error);
        showNotification('AI strateji uygulanırken hata oluştu: ' + error.message, 'error');
        // Hatayı fırlatma, döngünün devam etmesini sağla
        return false; // Başarısız olduğunu belirt
    }
}

// Optimizasyon geçmişini güncelle
function updateOptimizationHistory() {
    // Grafik güncelle
    updateOptimizationChart();

    // Tablo güncelle
    const tbody = document.querySelector('#optimizationHistoryTable tbody');
    tbody.innerHTML = '';

    optimizationHistory.forEach((history, index) => {
        const row = document.createElement('tr');

        const improvement = index > 0 ?
            (history.stats.averageProfit - optimizationHistory[index - 1].stats.averageProfit).toFixed(2) :
            'Başlangıç';

        const status = history.stats.averageProfit > 0 ?
            '<span class="badge bg-success">Karlı</span>' :
            '<span class="badge bg-danger">Zararlı</span>';

        row.innerHTML = `
            <td><strong>${history.iteration}</strong></td>
            <td class="${history.stats.averageProfit >= 0 ? 'text-success' : 'text-danger'}">
                ${history.stats.averageProfit.toFixed(2)}%
            </td>
            <td>${history.stats.profitableCount}/${history.stats.totalSymbols}</td>
            <td>${(history.stats.averageWinRate * 100).toFixed(1)}%</td>
            <td>${history.stats.averageTrades.toFixed(1)}</td>
            <td class="${improvement !== 'Başlangıç' && parseFloat(improvement) > 0 ? 'text-success' : 'text-danger'}">
                ${improvement !== 'Başlangıç' ? improvement + '%' : improvement}
            </td>
            <td>${status}</td>
        `;

        tbody.appendChild(row);
    });
}

// Optimizasyon grafiğini güncelle
function updateOptimizationChart() {
    if (optimizationHistory.length === 0) return;

    // Plotly yüklü mü kontrol et
    if (typeof Plotly === 'undefined') {
        console.warn('Plotly kütüphanesi yüklenmemiş, grafik çizilemedi');
        return;
    }

    const iterations = optimizationHistory.map(h => `İterasyon ${h.iteration}`);
    const profits = optimizationHistory.map(h => h.stats.averageProfit);
    const profitableCounts = optimizationHistory.map(h => h.stats.profitableCount);

    const trace1 = {
        x: iterations,
        y: profits,
        type: 'scatter',
        mode: 'lines+markers',
        name: 'Ortalama Kar %',
        line: { color: '#28a745' },
        yaxis: 'y'
    };

    const trace2 = {
        x: iterations,
        y: profitableCounts,
        type: 'scatter',
        mode: 'lines+markers',
        name: 'Karlı Para Birimi',
        line: { color: '#007bff' },
        yaxis: 'y2'
    };

    const layout = {
        title: 'Döngüsel Optimizasyon İlerlemesi',
        xaxis: { title: 'İterasyon' },
        yaxis: {
            title: 'Ortalama Kar (%)',
            side: 'left'
        },
        yaxis2: {
            title: 'Karlı Para Birimi Sayısı',
            side: 'right',
            overlaying: 'y'
        },
        height: 300
    };

    Plotly.newPlot('optimizationChart', [trace1, trace2], layout);
}

// Final optimizasyon sonuçlarını göster
function showFinalOptimizationResults(bestResult) {
    const message = `
        🎯 Döngüsel Optimizasyon Tamamlandı!

        📊 En İyi Sonuç:
        • Ortalama Kar: ${bestResult.averageProfit.toFixed(2)}%
        • Karlı Para Birimi: ${bestResult.profitableCount}/${bestResult.totalSymbols}
        • Kazanma Oranı: ${(bestResult.averageWinRate * 100).toFixed(1)}%
        • Ortalama İşlem: ${bestResult.averageTrades.toFixed(1)}

        🔄 Toplam İterasyon: ${optimizationHistory.length}
    `;

    showNotification(message, 'success');
}

// Bekleme fonksiyonu
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

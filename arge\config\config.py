"""
Proje konfigürasyon ayarları
"""

class Config:
    # Flask ayarları
    SECRET_KEY = 'gizli-anahtar-buraya'
    DEBUG = True
    
    # Binance API ayarları
    BINANCE_API_URL = 'https://api.binance.com/api/v3'
    
    # Veri çekme ayarları
    DEFAULT_SYMBOL = 'BTCUSDT'
    DEFAULT_INTERVAL = '1h'
    DEFAULT_LIMIT = 1000
    
    # Simülasyon ayarları
    INITIAL_BALANCE = 10000
    LEVERAGE = 1
    COMMISSION_RATE = 0.001
    TAKE_PROFIT_PERCENTAGE = 5
    STOP_LOSS_PERCENTAGE = 10 
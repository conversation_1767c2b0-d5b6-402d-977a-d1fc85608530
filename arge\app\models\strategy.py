"""
Simülasyon stratejilerini içeren sınıf
"""
import pandas as pd
import numpy as np
from typing import Dict
from .indicators import TechnicalIndicators

class TradingStrategy:
    def __init__(self, config):
        """
        TradingStrategy sınıfının başlatıcı metodu
        
        Args:
            config: Konfigürasyon nesnesi
        """
        self.config = config
        self.indicators = TechnicalIndicators()
        
        # Risk yönetimi parametreleri - Optimize edildi
        self.stop_loss_pct = 0.03  # %3 stop loss (daha geniş)
        self.take_profit_pct = 0.06  # %6 take profit (daha yüksek hedef)
        self.max_position_size = 0.15  # Maksimum pozisyon büyüklüğü (toplam bakiyenin %15'i)
        self.trailing_stop_pct = 0.015  # %1.5 trailing stop (daha geniş)
        
    def calculate_position_size(self, balance: float, current_price: float, risk_per_trade: float = 0.02) -> float:
        """
        Pozisyon büyüklüğünü hesaplar - Optimize edildi

        Args:
            balance (float): Mevcut bakiye
            current_price (float): Mevcut fiyat
            risk_per_trade (float): İşlem başına risk yüzdesi (artırıldı %2)

        Returns:
            float: Pozisyon büyüklüğü
        """
        # Risk bazlı pozisyon büyüklüğü hesaplama
        risk_amount = balance * risk_per_trade
        stop_loss_amount = current_price * self.stop_loss_pct
        position_size = risk_amount / stop_loss_amount

        # Maksimum pozisyon büyüklüğü kontrolü
        max_size = balance * self.max_position_size / current_price

        # Minimum pozisyon büyüklüğü kontrolü (çok küçük pozisyonları engelle)
        min_size = balance * 0.01 / current_price  # En az %1 bakiye

        final_size = min(position_size, max_size)
        return max(final_size, min_size)
        
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Teknik indikatörlere göre alım/satım sinyalleri üretir
        
        Args:
            df (pd.DataFrame): Fiyat ve indikatör verileri
            
        Returns:
            pd.DataFrame: Sinyaller eklenmiş DataFrame
        """
        print("generate_signals fonksiyonu çalışıyor") # Test logu
        # İndikatörleri hesapla
        df = self.indicators.add_all_indicators(df)
        
        # Sinyal sütununu oluştur (0: nötr, 1: al, -1: sat)
        df['signal'] = 0
        
        # Trend gücü hesapla
        df['trend_strength'] = abs(df['ema_20'] - df['ema_50']) / df['ema_50']
        
        # Volatilite hesapla
        df['volatility'] = df['bb_upper'] - df['bb_lower']
        df['volatility_ratio'] = df['volatility'] / df['close']
        
        # EMA Crossover Stratejisi - Optimize edildi
        df['ema_cross'] = np.where(
            (df['ema_20'] > df['ema_50']) & (df['ema_20'].shift(1) <= df['ema_50'].shift(1)) & (df['trend_strength'] > 0.005),
            1,  # Alış sinyali
            np.where(
                (df['ema_20'] < df['ema_50']) & (df['ema_20'].shift(1) >= df['ema_50'].shift(1)) & (df['trend_strength'] > 0.005),
                -1,  # Satış sinyali
                0  # Nötr
            )
        )

        # RSI Stratejisi - Daha esnek eşikler
        df['rsi_signal'] = np.where(
            (df['rsi'] < 40) & (df['rsi'].shift(1) >= 40) & (df['volatility_ratio'] < 0.03),
            1,  # Aşırı satım - Alış sinyali
            np.where(
                (df['rsi'] > 60) & (df['rsi'].shift(1) <= 60) & (df['volatility_ratio'] < 0.03),
                -1,  # Aşırı alım - Satış sinyali
                0  # Nötr
            )
        )

        # MACD Stratejisi - Histogram momentum eklendi
        df['macd_signal'] = np.where(
            (df['macd'] > df['macd_signal']) & (df['macd'].shift(1) <= df['macd_signal'].shift(1)) &
            (df['macd_hist'] > df['macd_hist'].shift(1)),  # Histogram artıyor
            1,  # MACD sinyal çizgisini yukarı kesiyor - Alış sinyali
            np.where(
                (df['macd'] < df['macd_signal']) & (df['macd'].shift(1) >= df['macd_signal'].shift(1)) &
                (df['macd_hist'] < df['macd_hist'].shift(1)),  # Histogram azalıyor
                -1,  # MACD sinyal çizgisini aşağı kesiyor - Satış sinyali
                0  # Nötr
            )
        )

        # Bollinger Bantları Stratejisi - Orta bant desteği eklendi
        df['bb_signal'] = np.where(
            ((df['close'] < df['bb_lower']) |
             ((df['close'] < df['bb_middle']) & (df['close'].shift(1) >= df['bb_middle']))) &
            (df['volatility_ratio'] < 0.03),
            1,  # Fiyat alt bandın altında veya orta bandı aşağı kesiyor - Alış sinyali
            np.where(
                ((df['close'] > df['bb_upper']) |
                 ((df['close'] > df['bb_middle']) & (df['close'].shift(1) <= df['bb_middle']))) &
                (df['volatility_ratio'] < 0.03),
                -1,  # Fiyat üst bandın üstünde veya orta bandı yukarı kesiyor - Satış sinyali
                0  # Nötr
            )
        )
        
        # Tüm stratejileri birleştir - Optimize edildi
        # Ağırlıklı sinyal sistemi
        df['weighted_signal'] = (
            df['ema_cross'] * 0.3 +      # EMA crossover %30 ağırlık
            df['rsi_signal'] * 0.25 +    # RSI %25 ağırlık
            df['macd_signal'] * 0.25 +   # MACD %25 ağırlık
            df['bb_signal'] * 0.2        # Bollinger Bands %20 ağırlık
        )

        # Sinyal eşikleri - Daha esnek
        df['signal'] = np.where(
            (df['weighted_signal'] >= 0.4) & (df['trend_strength'] > 0.003) & (df['volatility_ratio'] < 0.04),
            1,  # Alış sinyali
            np.where(
                (df['weighted_signal'] <= -0.4) & (df['trend_strength'] > 0.003) & (df['volatility_ratio'] < 0.04),
                -1,  # Satış sinyali
                0   # Nötr
            )
        )

        # Momentum filtresi ekleme
        df['price_momentum'] = df['close'].pct_change(3)  # 3 periyotluk momentum

        # Momentum ile sinyal filtreleme
        df['signal'] = np.where(
            (df['signal'] == 1) & (df['price_momentum'] > -0.02),  # Alış sinyali ve momentum çok negatif değil
            1,
            np.where(
                (df['signal'] == -1) & (df['price_momentum'] < 0.02),  # Satış sinyali ve momentum çok pozitif değil
                -1,
                0
            )
        )
        
        # Detaylı loglama
        for i in range(1, len(df)):
            if pd.notna(df['close'].iloc[i]): # Geçerli veri kontrolü
                timestamp = df.index[i].strftime('%Y-%m-%d %H:%M:%S')
                close_price = df['close'].iloc[i]

                ema20 = df['ema_20'].iloc[i]
                ema50 = df['ema_50'].iloc[i]
                rsi_val = df['rsi'].iloc[i]
                macd_val = df['macd'].iloc[i]
                macd_signal_val = df['macd_signal'].iloc[i]
                macd_hist_val = df['macd_hist'].iloc[i]
                bb_upper_val = df['bb_upper'].iloc[i]
                bb_lower_val = df['bb_lower'].iloc[i]

                ema_cross_sig = df['ema_cross'].iloc[i]
                rsi_sig = df['rsi_signal'].iloc[i]
                macd_sig = df['macd_signal'].iloc[i]
                bb_sig = df['bb_signal'].iloc[i]
                final_signal = df['signal'].iloc[i]

                # Loglama - Değerlerin sayı olup olmadığını kontrol et ve formatla
                ema20_str = f"{ema20:.2f}" if pd.notna(ema20) else "N/A"
                ema50_str = f"{ema50:.2f}" if pd.notna(ema50) else "N/A"
                rsi_val_str = f"{rsi_val:.2f}" if pd.notna(rsi_val) else "N/A"
                macd_val_str = f"{macd_val:.2f}" if pd.notna(macd_val) else "N/A"
                macd_signal_val_str = f"{macd_signal_val:.2f}" if pd.notna(macd_signal_val) else "N/A"
                macd_hist_val_str = f"{macd_hist_val:.2f}" if pd.notna(macd_hist_val) else "N/A"
                bb_upper_val_str = f"{bb_upper_val:.2f}" if pd.notna(bb_upper_val) else "N/A"
                bb_lower_val_str = f"{bb_lower_val:.2f}" if pd.notna(bb_lower_val) else "N/A"
                ema_cross_sig_str = f"{ema_cross_sig:.0f}" if pd.notna(ema_cross_sig) else "N/A"
                rsi_sig_str = f"{rsi_sig:.0f}" if pd.notna(rsi_sig) else "N/A"
                macd_sig_str = f"{macd_sig:.0f}" if pd.notna(macd_sig) else "N/A"
                bb_sig_str = f"{bb_sig:.0f}" if pd.notna(bb_sig) else "N/A"
                final_signal_str = f"{final_signal:.0f}" if pd.notna(final_signal) else "N/A"

                print(f"\n--- Zaman: {timestamp}, Fiyat: {close_price:.2f} ---")
                print(f"EMA(20): {ema20_str}, EMA(50): {ema50_str} -> EMA Sinyal: {ema_cross_sig_str}")
                print(f"RSI: {rsi_val_str} -> RSI Sinyal: {rsi_sig_str}")
                print(f"MACD: {macd_val_str}, MACD Sinyal Hattı: {macd_signal_val_str}, Histogram: {macd_hist_val_str} -> MACD Sinyal: {macd_sig_str}")
                print(f"BB (Üst: {bb_upper_val_str}, Alt: {bb_lower_val_str}) -> BB Sinyal: {bb_sig_str}")
                print(f"Nihai Sinyal: {final_signal_str}")
        
        return df
    
    def backtest(self, df: pd.DataFrame, initial_balance: float = 10000) -> Dict:
        """
        Stratejiyi geçmiş veriler üzerinde test eder
        
        Args:
            df (pd.DataFrame): Fiyat ve sinyal verileri
            initial_balance (float): Başlangıç bakiyesi
            
        Returns:
            Dict: Backtest sonuçları
        """
        balance = initial_balance
        position = 0  # 0: pozisyon yok, 1: long, -1: short
        position_size = 0
        entry_price = 0
        highest_price = 0
        lowest_price = float('inf')
        trades = []
        log = []
        
        # Komisyon oranı
        commission_rate = self.config.get('COMMISSION_RATE', 0.0002) # Varsayılan %0.02
        
        for i in range(1, len(df)):
            current_price = df['close'].iloc[i]
            signal = df['signal'].iloc[i]
            
            # Trailing stop kontrolü
            if position == 1:  # Long pozisyon
                highest_price = max(highest_price, current_price)
                if current_price < highest_price * (1 - self.trailing_stop_pct):
                    # Trailing stop tetiklendi
                    profit = (current_price - entry_price) * position_size
                    commission = current_price * position_size * commission_rate
                    balance += profit - commission # Komisyonu düş
                    trades.append({
                        'type': 'close_long_trailing',
                        'price': current_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'profit': profit,
                        'balance': balance,
                        'commission': commission # Komisyon bilgisini ekle
                    })
                    net_profit = profit - commission
                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon trailing stop ile kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                    position = 0
                    position_size = 0
                    entry_price = 0
                    highest_price = 0
                    
            elif position == -1:  # Short pozisyon
                lowest_price = min(lowest_price, current_price)
                if current_price > lowest_price * (1 + self.trailing_stop_pct):
                    # Trailing stop tetiklendi
                    profit = (entry_price - current_price) * position_size
                    commission = current_price * position_size * commission_rate
                    balance += profit - commission # Komisyonu düş
                    trades.append({
                        'type': 'close_short_trailing',
                        'price': current_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'profit': profit,
                        'balance': balance,
                        'commission': commission # Komisyon bilgisini ekle
                    })
                    net_profit = profit - commission
                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - SHORT Pozisyon trailing stop ile kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                    position = 0
                    position_size = 0
                    entry_price = 0
                    lowest_price = float('inf')
            
            # Stop loss ve take profit kontrolü
            if position != 0:
                if position == 1:  # Long pozisyon
                    if current_price <= entry_price * (1 - self.stop_loss_pct):
                        # Stop loss tetiklendi
                        loss = (current_price - entry_price) * position_size
                        commission = current_price * position_size * commission_rate
                        balance += loss - commission # Komisyonu düş
                        trades.append({
                            'type': 'close_long_stop',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': loss,
                            'balance': balance,
                            'commission': commission # Komisyon bilgisini ekle
                        })
                        net_profit = loss - commission # loss zaten negatif
                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon stop loss ile kapatıldı. Brüt Kar/Zarar: {loss:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                        position = 0
                        position_size = 0
                        entry_price = 0
                        highest_price = 0
                    elif current_price >= entry_price * (1 + self.take_profit_pct):
                        # Take profit tetiklendi
                        profit = (current_price - entry_price) * position_size
                        commission = current_price * position_size * commission_rate
                        balance += profit - commission # Komisyonu düş
                        trades.append({
                            'type': 'close_long_profit',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': profit,
                            'balance': balance,
                            'commission': commission # Komisyon bilgisini ekle
                        })
                        net_profit = profit - commission
                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon take profit ile kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                        position = 0
                        position_size = 0
                        entry_price = 0
                        highest_price = 0
                        
                elif position == -1:  # Short pozisyon
                    if current_price >= entry_price * (1 + self.stop_loss_pct):
                        # Stop loss tetiklendi
                        loss = (entry_price - current_price) * position_size
                        commission = current_price * position_size * commission_rate
                        balance += loss - commission # Komisyonu düş
                        trades.append({
                            'type': 'close_short_stop',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': loss,
                            'balance': balance,
                            'commission': commission # Komisyon bilgisini ekle
                        })
                        net_profit = loss - commission # loss zaten negatif
                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - SHORT Pozisyon stop loss ile kapatıldı. Brüt Kar/Zarar: {loss:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                        position = 0
                        position_size = 0
                        entry_price = 0
                        lowest_price = float('inf')
                    elif current_price <= entry_price * (1 - self.take_profit_pct):
                        # Take profit tetiklendi
                        profit = (entry_price - current_price) * position_size
                        commission = current_price * position_size * commission_rate
                        balance += profit - commission # Komisyonu düş
                        trades.append({
                            'type': 'close_short_profit',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': profit,
                            'balance': balance,
                            'commission': commission # Komisyon bilgisini ekle
                        })
                        net_profit = profit - commission
                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - SHORT Pozisyon take profit ile kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                        position = 0
                        position_size = 0
                        entry_price = 0
                        lowest_price = float('inf')
            
            # Yeni pozisyon açma
            if signal == 1 and position <= 0:  # Alış sinyali
                if position == -1:  # Short pozisyonu kapat
                    profit = (entry_price - current_price) * position_size
                    commission = current_price * position_size * commission_rate
                    balance += profit - commission # Komisyonu düş
                    trades.append({
                        'type': 'close_short',
                        'price': current_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'profit': profit,
                        'balance': balance,
                        'commission': commission # Komisyon bilgisini ekle
                    })
                    net_profit = profit - commission
                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - SHORT Pozisyon kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                
                # Long pozisyon aç
                position = 1
                position_size = self.calculate_position_size(balance, current_price)
                entry_price = current_price
                highest_price = current_price
                
                # Alış işlemi komisyonu
                commission = current_price * position_size * commission_rate
                balance -= commission # Alış komisyonu düş
                
                trades.append({
                    'type': 'open_long',
                    'price': current_price,
                    'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                    'size': position_size,
                    'balance': balance, # Komisyon düşülmüş bakiye
                    'commission': commission # Komisyon bilgisini ekle
                })
                log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon açıldı. Açılış Fiyatı: {entry_price:.2f}, Pozisyon Büyüklüğü: {position_size:.4f}, Komisyon: {commission:.2f}, Kalan Bakiye: {balance:.2f}") # Loga komisyonu ve kalan bakiyeyi ekle
                
            elif signal == -1 and position >= 0:  # Satış sinyali
                if position == 1:  # Long pozisyonu kapat
                    profit = (current_price - entry_price) * position_size
                    commission = current_price * position_size * commission_rate
                    balance += profit - commission # Komisyonu düş
                    trades.append({
                        'type': 'close_long',
                        'price': current_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'profit': profit,
                        'balance': balance,
                        'commission': commission # Komisyon bilgisini ekle
                    })
                    net_profit = profit - commission
                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                
                # Short pozisyon aç
                position = -1
                position_size = self.calculate_position_size(balance, current_price)
                entry_price = current_price
                lowest_price = current_price
                
                # Satış işlemi komisyonu
                commission = current_price * position_size * commission_rate
                balance -= commission # Satış komisyonu düş
                
                trades.append({
                    'type': 'open_short',
                    'price': current_price,
                    'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                    'size': position_size,
                    'balance': balance, # Komisyon düşülmüş bakiye
                    'commission': commission # Komisyon bilgisini ekle
                })
                log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - SHORT Pozisyon açıldı. Açılış Fiyatı: {entry_price:.2f}, Pozisyon Büyüklüğü: {position_size:.4f}, Komisyon: {commission:.2f}, Kalan Bakiye: {balance:.2f}") # Loga komisyonu ve kalan bakiyeyi ekle
        
        # Son pozisyonu kapat
        if position != 0:
            current_price = df['close'].iloc[-1]
            if position == 1:
                profit = (current_price - entry_price) * position_size
            else:
                profit = (entry_price - current_price) * position_size
            
            # Kapatma işlemi komisyonu
            commission = current_price * position_size * commission_rate
            balance += profit - commission # Komisyonu düş
            
            trades.append({
                'type': 'close_position',
                'price': current_price,
                'timestamp': df.index[-1].strftime('%Y-%m-%d %H:%M:%S'),
                'profit': profit,
                'balance': balance,
                'commission': commission # Komisyon bilgisini ekle
            })
            net_profit = profit - commission
            log.append(f"{df.index[-1].strftime('%Y-%m-%d %H:%M:%S')} - Kalan Pozisyon kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
        
        # Performans metriklerini hesapla
        total_trades = len([t for t in trades if t['type'] in ['open_long', 'open_short']])
        # Tüm kapatma işlemlerinin net karını/zararını hesaplayarak kazanan ve kaybeden işlemleri say
        winning_trades = 0
        losing_trades = 0
        
        for trade in trades:
            # Kapatma işlemlerini kontrol et
            if trade['type'].startswith('close'):
                # Net kar/zarar = Brüt Kar/Zarar - Komisyon (komisyon yoksa 0 kabul et)
                net_result = trade['profit'] - trade.get('commission', 0)
                
                if net_result > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        total_profit = balance - initial_balance
        profit_percentage = (total_profit / initial_balance) * 100
        
        # Backtest başlangıç tarihini DataFrame'in ilk indeksinden al
        start_date = df.index[0].strftime('%Y-%m-%d %H:%M:%S') if not df.empty else 'N/A'
        
        return {
            'initial_balance': initial_balance,
            'final_balance': balance,
            'total_profit': total_profit,
            'profit_percentage': profit_percentage,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'trades': trades,
            'log': log,
            'symbol': self.config.get('DEFAULT_SYMBOL', 'N/A'), # Konfigürasyondan sembolü al
            'timeframe': self.config.get('DEFAULT_TIMEFRAME', 'N/A'), # Konfigürasyondan zaman aralığını al
            'start_date': start_date # Hesaplanan başlangıç tarihini ekle
        } 
"""
Ana controller sınıfı
"""
from flask import render_template, jsonify, request, session
from app.models.data_fetcher import DataFetcher
from app.models.simulation import SimulationEngine
from app.models.config import Config
from datetime import datetime

class MainController:
    def __init__(self):
        """
        MainController sınıfının başlatıcı metodu
        """
        self.config = Config()
        self.data_fetcher = DataFetcher(self.config)
        self.simulation_engine = SimulationEngine(self.config)
        
    def index(self):
        """
        Ana sayfa
        
        Returns:
            str: HTML sayfası
        """
        return render_template('index.html')
    
    def get_market_data(self):
        """
        Piyasa verilerini döndürür
        
        Returns:
            dict: <PERSON>yasa verileri
        """
        symbol = request.args.get('symbol', self.config.get('DEFAULT_SYMBOL'))
        timeframe = request.args.get('timeframe', self.config.get('DEFAULT_TIMEFRAME'))
        start_date = request.args.get('start_date', self.config.get('DEFAULT_START_DATE'))
        
        df = self.data_fetcher.fetch_ohlcv(symbol, timeframe, start_date)
        
        return jsonify({
            'symbol': symbol,
            'timeframe': timeframe,
            'data': df.to_dict(orient='records')
        })
    
    def get_exchange_info(self):
        """
        Borsa bilgilerini döndürür
        
        Returns:
            dict: Borsa bilgileri
        """
        symbols = self.data_fetcher.get_available_symbols()
        timeframes = self.data_fetcher.get_timeframes()
        
        return jsonify({
            'symbols': symbols,
            'timeframes': timeframes
        })
    
    def run_simulation(self):
        """
        Simülasyonu çalıştırır
        
        Returns:
            dict: Simülasyon sonuçları
        """
        try:
            data = request.get_json()
            
            if not data:
                return jsonify({'error': 'Geçersiz istek verisi'}), 400
            
            symbol = data.get('symbol')
            timeframe = data.get('timeframe')
            start_date = data.get('start_date')
            initial_balance = data.get('initial_balance')
            
            # Parametreleri doğrula
            if not all([symbol, timeframe, start_date, initial_balance]):
                return jsonify({'error': 'Eksik parametreler'}), 400
            
            try:
                initial_balance = float(initial_balance)
                if initial_balance <= 0:
                    return jsonify({'error': 'Başlangıç bakiyesi pozitif olmalıdır'}), 400
            except ValueError:
                return jsonify({'error': 'Geçersiz başlangıç bakiyesi'}), 400
            
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                return jsonify({'error': 'Geçersiz tarih formatı'}), 400
            
            # Verileri çek
            df = self.data_fetcher.fetch_ohlcv(symbol, timeframe, start_date)
            
            if df.empty:
                return jsonify({'error': 'Veri bulunamadı'}), 404
            
            # Kullanıcı strateji ayarlarını al
            strategy_settings = session.get('strategy_settings', None)

            # Strateji ayarları varsa yeni simulation engine oluştur
            if strategy_settings:
                simulation_engine = SimulationEngine(self.config, strategy_settings)
            else:
                simulation_engine = self.simulation_engine

            # Simülasyonu çalıştır
            results = simulation_engine.run_simulation(df, initial_balance)

            # Fiyat verilerini ekle
            results['prices'] = df['close'].tolist()

            # Kullanıcı parametrelerini sonuçlara ekle
            results['symbol'] = symbol
            results['timeframe'] = timeframe
            results['start_date'] = start_date
            results['initial_balance'] = initial_balance

            return jsonify(results)
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    def get_performance(self):
        """
        Performans metriklerini döndürür
        
        Returns:
            dict: Performans metrikleri
        """
        data = request.get_json()
        results = data.get('results', {})
        
        metrics = self.simulation_engine.get_performance_metrics(results)
        
        return jsonify(metrics)
    
    def get_trade_history(self):
        """
        İşlem geçmişini döndürür
        
        Returns:
            dict: İşlem geçmişi
        """
        data = request.get_json()
        results = data.get('results', {})
        
        trades = self.simulation_engine.get_trade_history(results)
        
        return jsonify(trades)

    def strategy_page(self):
        """
        Strateji ayarları sayfasını gösterir

        Returns:
            str: HTML sayfası
        """
        return render_template('strategy.html')

    def save_strategy(self):
        """
        Strateji ayarlarını kaydeder

        Returns:
            dict: Kaydetme sonucu
        """
        try:
            data = request.get_json()

            if not data:
                return jsonify({'success': False, 'error': 'Geçersiz veri'}), 400

            # Strateji ayarlarını doğrula
            required_fields = [
                'stopLoss', 'takeProfit', 'trailingStop', 'basePosition',
                'maxPosition', 'minPosition', 'rsiLower', 'rsiUpper',
                'emaSpread', 'momentumScore', 'priceChange3', 'priceChange10',
                'volatilityMax', 'commission'
            ]

            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Eksik alan: {field}'}), 400

            # Strateji ayarlarını session'a kaydet (gerçek uygulamada veritabanına kaydedilir)
            session['strategy_settings'] = data

            return jsonify({'success': True, 'message': 'Strateji başarıyla kaydedildi'})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    def get_strategy(self):
        """
        Kaydedilmiş strateji ayarlarını döndürür

        Returns:
            dict: Strateji ayarları
        """
        strategy_settings = session.get('strategy_settings', {})
        return jsonify(strategy_settings)

    def bulk_analysis_page(self):
        """
        Toplu analiz sayfasını gösterir

        Returns:
            str: HTML sayfası
        """
        return render_template('bulk_analysis.html')

    def generate_ai_strategy(self):
        """
        Toplu analiz sonuçlarından AI strateji önerisi oluşturur

        Returns:
            dict: AI strateji önerisi
        """
        try:
            data = request.get_json()
            analysis_results = data.get('analysis_results', [])

            if not analysis_results:
                return jsonify({'error': 'Analiz sonuçları bulunamadı'}), 400

            # Başarılı sonuçları filtrele
            successful_results = [r for r in analysis_results if not r.get('error')]

            if len(successful_results) < 3:
                return jsonify({'error': 'Yeterli başarılı analiz sonucu yok (minimum 3 gerekli)'}), 400

            # AI analizi yap
            ai_strategy = self._analyze_and_generate_strategy(successful_results)

            return jsonify(ai_strategy)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    def _analyze_and_generate_strategy(self, results):
        """
        Analiz sonuçlarından strateji parametreleri oluşturur

        Args:
            results: Başarılı analiz sonuçları listesi

        Returns:
            dict: AI strateji önerisi
        """
        # Karlı sonuçları al
        profitable_results = [r for r in results if r.get('profit_percentage', 0) > 0]

        # İstatistikleri hesapla
        total_results = len(results)
        profitable_count = len(profitable_results)
        profitability_rate = profitable_count / total_results if total_results > 0 else 0

        avg_profit = sum(r.get('profit_percentage', 0) for r in profitable_results) / len(profitable_results) if profitable_results else 0
        avg_win_rate = sum(r.get('win_rate', 0) for r in results) / len(results) if results else 0
        avg_trades = sum(r.get('total_trades', 0) for r in results) / len(results) if results else 0
        avg_sharpe = sum(r.get('sharpe_ratio', 0) for r in results) / len(results) if results else 0
        avg_drawdown = sum(r.get('max_drawdown', 0) for r in results) / len(results) if results else 0

        # Strateji parametrelerini belirle
        strategy_params = self._determine_strategy_parameters(
            profitability_rate, avg_profit, avg_win_rate, avg_trades, avg_sharpe, avg_drawdown
        )

        # Analiz metni oluştur
        analysis_text = self._generate_analysis_text(
            total_results, profitable_count, profitability_rate, avg_profit, avg_win_rate, avg_trades
        )

        return {
            'analysis': analysis_text,
            'parameters': strategy_params,
            'expected': {
                'winRate': f"{avg_win_rate * 100:.1f}",
                'avgProfit': f"{avg_profit:.1f}",
                'riskLevel': self._determine_risk_level(avg_drawdown, avg_sharpe)
            }
        }

    def _determine_strategy_parameters(self, profitability_rate, avg_profit, avg_win_rate, avg_trades, avg_sharpe, avg_drawdown):
        """
        İstatistiklere göre strateji parametrelerini belirler
        """
        # Temel parametreler
        params = {
            'stopLoss': 4,
            'takeProfit': 8,
            'trailingStop': 3,
            'basePosition': 15,
            'maxPosition': 25,
            'minPosition': 8,
            'rsiLower': 55,
            'rsiUpper': 75,
            'emaSpread': 0.2,
            'momentumScore': 5,
            'priceChange3': 0.5,
            'priceChange10': 1.0,
            'volatilityMax': 4,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

        # Karlılık oranına göre ayarlama
        if profitability_rate > 0.7:  # %70+ karlı
            # Agresif strateji
            params['takeProfit'] = 12
            params['basePosition'] = 20
            params['maxPosition'] = 30
            params['momentumScore'] = 4
        elif profitability_rate < 0.4:  # %40- karlı
            # Konservatif strateji
            params['stopLoss'] = 3
            params['takeProfit'] = 6
            params['basePosition'] = 10
            params['maxPosition'] = 15
            params['momentumScore'] = 6
            params['volatilityMax'] = 2

        # Kazanma oranına göre ayarlama
        if avg_win_rate < 0.4:  # Düşük kazanma oranı
            params['stopLoss'] = max(2, params['stopLoss'] - 1)
            params['takeProfit'] = min(15, params['takeProfit'] + 2)
        elif avg_win_rate > 0.6:  # Yüksek kazanma oranı
            params['takeProfit'] = max(6, params['takeProfit'] - 2)
            params['basePosition'] = min(25, params['basePosition'] + 5)

        # İşlem sayısına göre ayarlama
        if avg_trades > 50:  # Çok fazla işlem
            params['momentumScore'] = min(6, params['momentumScore'] + 1)
            params['volatilityMax'] = max(2, params['volatilityMax'] - 1)
        elif avg_trades < 10:  # Az işlem
            params['momentumScore'] = max(3, params['momentumScore'] - 1)
            params['volatilityMax'] = min(8, params['volatilityMax'] + 1)

        # Sharpe oranına göre ayarlama
        if avg_sharpe < 0:  # Negatif Sharpe
            params['stopLoss'] = max(2, params['stopLoss'] - 1)
            params['basePosition'] = max(8, params['basePosition'] - 3)

        # Drawdown'a göre ayarlama
        if avg_drawdown > 0.2:  # Yüksek drawdown
            params['stopLoss'] = max(2, params['stopLoss'] - 1)
            params['trailingStop'] = max(1, params['trailingStop'] - 1)
            params['basePosition'] = max(8, params['basePosition'] - 5)

        return params

    def _generate_analysis_text(self, total_results, profitable_count, profitability_rate, avg_profit, avg_win_rate, avg_trades):
        """
        Analiz sonuçlarına göre açıklama metni oluşturur
        """
        text = f"Toplam {total_results} para biriminde yapılan analiz sonucunda, "
        text += f"{profitable_count} tanesi karlı çıktı (%{profitability_rate*100:.1f} başarı oranı). "

        if profitability_rate > 0.6:
            text += "Bu oldukça iyi bir sonuç! Mevcut strateji genel olarak başarılı görünüyor. "
        elif profitability_rate > 0.4:
            text += "Orta seviyede bir başarı oranı. Strateji parametrelerinde iyileştirme yapılabilir. "
        else:
            text += "Düşük başarı oranı. Strateji parametrelerinde önemli değişiklikler gerekli. "

        text += f"Ortalama kazanma oranı %{avg_win_rate*100:.1f}, "
        text += f"ortalama işlem sayısı {avg_trades:.0f}. "

        if avg_profit > 5:
            text += "Karlı işlemlerde yüksek getiri elde ediliyor. "
        elif avg_profit > 2:
            text += "Karlı işlemlerde orta seviyede getiri elde ediliyor. "
        else:
            text += "Karlı işlemlerde düşük getiri elde ediliyor. "

        text += "Aşağıdaki parametreler bu analize göre optimize edilmiştir."

        return text

    def _determine_risk_level(self, avg_drawdown, avg_sharpe):
        """
        Risk seviyesini belirler
        """
        if avg_drawdown > 0.3 or avg_sharpe < -1:
            return "Yüksek"
        elif avg_drawdown > 0.15 or avg_sharpe < 0:
            return "Orta"
        else:
            return "Düşük"
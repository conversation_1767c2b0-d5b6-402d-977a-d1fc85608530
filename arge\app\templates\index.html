{% extends "base.html" %}

{% block title %}Kripto Simülasyon{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sol Panel - Kontroller -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Simülasyon Ayarları</h5>
                </div>
                <div class="card-body">
                    <form id="simulationForm">
                        <div class="mb-3">
                            <label for="symbol" class="form-label">Sembol</label>
                            <select class="form-select" id="symbol" name="symbol">
                                <!-- Semboller JavaScript ile doldurulacak -->
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="timeframe" class="form-label">Zaman Dilimi</label>
                            <select class="form-select" id="timeframe" name="timeframe">
                                <!-- Zaman dilimleri JavaScript ile doldurulacak -->
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="startDate" class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="startDate" name="startDate">
                        </div>
                        
                        <div class="mb-3">
                            <label for="initialBalance" class="form-label">Başlangıç Bakiyesi (USDT)</label>
                            <input type="number" class="form-control" id="initialBalance" name="initialBalance" value="10000">
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">Simülasyonu Başlat</button>
                    </form>
                </div>
            </div>
            
            <!-- Performans Metrikleri -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">Performans Metrikleri</h5>
                </div>
                <div class="card-body">
                    <div id="performanceMetrics">
                        <!-- Metrikler JavaScript ile doldurulacak -->
                    </div>
                </div>
            </div>
            
            <!-- Analiz ve Karar Logları -->
            <div class="card mt-3">
                <div class="card-header">Analiz ve Karar Logları</div>
                <div class="card-body" id="simulationLog" style="max-height: 300px; overflow-y: auto; font-size: 0.8em;">
                    <!-- Log mesajları buraya yüklenecek -->
                    <p>Simülasyon başlatılmadı.</p>
                </div>
            </div>
        </div>
        
        <!-- Sağ Panel - Grafikler -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Fiyat Grafiği</h5>
                </div>
                <div class="card-body">
                    <div id="priceChart" style="height: 400px;"></div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">Performans Grafiği</h5>
                </div>
                <div class="card-body">
                    <div id="performanceChart" style="height: 400px;"></div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">İşlem Geçmişi</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="tradeHistory">
                            <thead>
                                <tr>
                                    <th>Tarih</th>
                                    <th>İşlem</th>
                                    <th>Fiyat</th>
                                    <th>Miktar</th>
                                    <th>Kar/Zarar</th>
                                    <th>Bakiye</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- İşlem geçmişi JavaScript ile doldurulacak -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="https://cdn.plot.ly/plotly-2.29.1.min.js"></script>
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
{% endblock %} 
/* <PERSON><PERSON> stiller */
body {
    background-color: #f8f9fa;
}

/* <PERSON>rt stilleri */
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

/* Form stilleri */
.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Grafik container */
#priceChart {
    min-height: 400px;
}

/* Responsive düzenlemeler */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    #priceChart {
        min-height: 300px;
    }
} 
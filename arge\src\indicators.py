"""
Teknik analiz göstergeleri ve sinyal üretimi için yardımcı fonksiyonlar
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional

def calculate_rsi(prices: np.ndarray, period: int = 14) -> np.ndarray:
    """
    RSI (Relative Strength Index) hesaplama
    
    Args:
        prices: Fiyat verileri
        period: RSI periyodu (varsayılan: 14)
    
    Returns:
        RSI değerleri
    """
    # Fiyat değişimlerini hesapla
    deltas = np.diff(prices)
    seed = deltas[:period+1]
    
    # Yukarı ve aşağı hareketleri ayır
    up = seed[seed >= 0].sum()/period
    down = -seed[seed < 0].sum()/period
    
    # İlk RSI değerini hesapla
    rs = up/down if down != 0 else 0
    rsi = np.zeros_like(prices)
    rsi[period] = 100. - 100./(1. + rs)
    
    # <PERSON>lan değerleri hesapla
    for i in range(period+1, len(prices)):
        delta = deltas[i-1]
        if delta > 0:
            upval = delta
            downval = 0.
        else:
            upval = 0.
            downval = -delta
            
        up = (up*(period-1) + upval)/period
        down = (down*(period-1) + downval)/period
        
        rs = up/down if down != 0 else 0
        rsi[i] = 100. - 100./(1. + rs)
    
    return rsi

def calculate_macd(prices: np.ndarray, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    MACD (Moving Average Convergence Divergence) hesaplama
    
    Args:
        prices: Fiyat verileri
        fast_period: Hızlı EMA periyodu
        slow_period: Yavaş EMA periyodu
        signal_period: Sinyal çizgisi periyodu
    
    Returns:
        MACD, Sinyal ve Histogram değerleri
    """
    # EMA'ları hesapla
    ema_fast = pd.Series(prices).ewm(span=fast_period, adjust=False).mean()
    ema_slow = pd.Series(prices).ewm(span=slow_period, adjust=False).mean()
    
    # MACD çizgisini hesapla
    macd_line = ema_fast - ema_slow
    
    # Sinyal çizgisini hesapla
    signal_line = pd.Series(macd_line).ewm(span=signal_period, adjust=False).mean()
    
    # Histogramı hesapla
    histogram = macd_line - signal_line
    
    return macd_line.values, signal_line.values, histogram.values

def calculate_bollinger_bands(prices: np.ndarray, period: int = 20, num_std: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Bollinger Bands hesaplama
    
    Args:
        prices: Fiyat verileri
        period: SMA periyodu
        num_std: Standart sapma çarpanı
    
    Returns:
        Orta bant, Üst bant ve Alt bant değerleri
    """
    # Orta bant (SMA)
    middle_band = pd.Series(prices).rolling(window=period).mean()
    
    # Standart sapma
    std = pd.Series(prices).rolling(window=period).std()
    
    # Üst ve alt bantlar
    upper_band = middle_band + (std * num_std)
    lower_band = middle_band - (std * num_std)
    
    return middle_band.values, upper_band.values, lower_band.values

def calculate_stochastic_oscillator(high: np.ndarray, low: np.ndarray, close: np.ndarray, 
                                  k_period: int = 14, d_period: int = 3) -> Tuple[np.ndarray, np.ndarray]:
    """
    Stochastic Osilatör hesaplama
    
    Args:
        high: Yüksek fiyatlar
        low: Düşük fiyatlar
        close: Kapanış fiyatları
        k_period: %K periyodu
        d_period: %D periyodu
    
    Returns:
        %K ve %D değerleri
    """
    # En düşük düşük ve en yüksek yüksek değerleri hesapla
    lowest_low = pd.Series(low).rolling(window=k_period).min()
    highest_high = pd.Series(high).rolling(window=k_period).max()
    
    # %K hesapla
    k = 100 * ((close - lowest_low) / (highest_high - lowest_low))
    
    # %D hesapla (K'nın SMA'sı)
    d = pd.Series(k).rolling(window=d_period).mean()
    
    return k.values, d.values

def calculate_trend_strength(prices: np.ndarray, period: int = 14) -> np.ndarray:
    """
    Trend gücünü hesapla
    
    Args:
        prices: Fiyat verileri
        period: Trend periyodu
    
    Returns:
        Trend gücü değerleri (-1 ile 1 arasında)
    """
    # Fiyat değişimlerini hesapla
    price_changes = np.diff(prices)
    
    # Trend gücünü hesapla
    trend_strength = np.zeros_like(prices)
    for i in range(period, len(prices)):
        window = price_changes[i-period:i]
        up_moves = np.sum(window[window > 0])
        down_moves = np.abs(np.sum(window[window < 0]))
        total_moves = up_moves + down_moves
        
        if total_moves > 0:
            trend_strength[i] = (up_moves - down_moves) / total_moves
        else:
            trend_strength[i] = 0
            
    return trend_strength

def analyze_market(data: pd.DataFrame) -> Dict:
    """
    Piyasa verilerini analiz et ve sinyaller üret
    
    Args:
        data: OHLCV verileri içeren DataFrame
    
    Returns:
        Analiz sonuçları ve sinyaller
    """
    # Fiyat verilerini al
    close_prices = data['close'].values
    high_prices = data['high'].values
    low_prices = data['low'].values
    
    # Teknik göstergeleri hesapla
    rsi = calculate_rsi(close_prices)
    macd_line, signal_line, histogram = calculate_macd(close_prices)
    middle_band, upper_band, lower_band = calculate_bollinger_bands(close_prices)
    k_line, d_line = calculate_stochastic_oscillator(high_prices, low_prices, close_prices)
    trend_strength = calculate_trend_strength(close_prices)
    
    # Sinyal üretimi için eşik değerleri - daha hassas değerler
    rsi_oversold = 40  # 35'ten 40'a yükseltildi
    rsi_overbought = 60  # 65'ten 60'a düşürüldü
    stoch_oversold = 30  # 25'ten 30'a yükseltildi
    stoch_overbought = 70  # 75'ten 70'e düşürüldü
    
    # Sinyal üret
    signals = np.zeros(len(close_prices))
    signal_strength = np.zeros(len(close_prices))
    
    print("\n--- Piyasa Analizi Başladı ---") # Loglama eklendi
    
    for i in range(2, len(close_prices)):
        # RSI sinyalleri - trend yönü ve momentum
        rsi_signal = 0
        rsi_change = rsi[i] - rsi[i-1]
        if (rsi[i] < rsi_oversold and rsi_change > 0) or \
           (rsi[i] < 50 and rsi_change > 2):  # RSI momentum değişimi
            rsi_signal = 1
        elif (rsi[i] > rsi_overbought and rsi_change < 0) or \
             (rsi[i] > 50 and rsi_change < -2):
            rsi_signal = -1
            
        # MACD sinyalleri - histogram değişimi ve momentum
        macd_signal = 0
        hist_change = histogram[i] - histogram[i-1]
        if (macd_line[i] > signal_line[i] and macd_line[i-1] <= signal_line[i-1]) or \
           (histogram[i] > 0 and hist_change > 0) or \
           (histogram[i-1] < 0 and histogram[i] > 0):  # Sıfır çizgisi geçişi
            macd_signal = 1
        elif (macd_line[i] < signal_line[i] and macd_line[i-1] >= signal_line[i-1]) or \
             (histogram[i] < 0 and hist_change < 0) or \
             (histogram[i-1] > 0 and histogram[i] < 0):
            macd_signal = -1
            
        # Bollinger Bands sinyalleri - bant yakınlığı ve momentum
        bb_signal = 0
        bb_range = upper_band[i] - lower_band[i]
        price_position = (close_prices[i] - lower_band[i]) / bb_range if bb_range != 0 else 0.5
        price_change = close_prices[i] - close_prices[i-1]
        
        if (close_prices[i] < lower_band[i] and price_change > 0) or \
           (price_position < 0.3 and price_change > 0) or \
           (close_prices[i] < middle_band[i] and price_change > 0 and close_prices[i-1] < close_prices[i-2]):
            bb_signal = 1
        elif (close_prices[i] > upper_band[i] and price_change < 0) or \
             (price_position > 0.7 and price_change < 0) or \
             (close_prices[i] > middle_band[i] and price_change < 0 and close_prices[i-1] > close_prices[i-2]):
            bb_signal = -1
            
        # Stochastic sinyalleri - kesişim ve momentum
        stoch_signal = 0
        k_change = k_line[i] - k_line[i-1]
        d_change = d_line[i] - d_line[i-1]
        
        if (k_line[i] < stoch_oversold and k_change > 0) or \
           (k_line[i] > d_line[i] and k_line[i-1] <= d_line[i-1] and k_line[i] < 50) or \
           (k_change > 0 and d_change > 0 and k_line[i] < 50):
            stoch_signal = 1
        elif (k_line[i] > stoch_overbought and k_change < 0) or \
             (k_line[i] < d_line[i] and k_line[i-1] >= d_line[i-1] and k_line[i] > 50) or \
             (k_change < 0 and d_change < 0 and k_line[i] > 50):
            stoch_signal = -1
            
        # Trend gücü sinyalleri
        trend_signal = 0
        if trend_strength[i] > 0.3 and trend_strength[i-1] <= 0.3:
            trend_signal = 1
        elif trend_strength[i] < -0.3 and trend_strength[i-1] >= -0.3:
            trend_signal = -1
            
        # Toplam sinyal hesapla (ağırlıklı ortalama)
        total_signal = (rsi_signal * 0.25 + 
                       macd_signal * 0.25 + 
                       bb_signal * 0.2 + 
                       stoch_signal * 0.2 +
                       trend_signal * 0.1)
        
        # Sinyal gücünü hesapla
        signal_strength[i] = abs(total_signal)
        
        # Sinyal eşiği kontrolü - daha düşük eşik
        if signal_strength[i] >= 0.2:  # 0.3'ten 0.2'ye düşürüldü
            signals[i] = np.sign(total_signal)
            
        # Her adım için loglama
        print(f"\nZaman Adımı: {i}")
        print(f"Kapanış Fiyatı: {close_prices[i]:.2f}")
        print(f"RSI: {rsi[i]:.2f}, Sinyal: {rsi_signal}")
        print(f"MACD Hattı: {macd_line[i]:.2f}, Sinyal Hattı: {signal_line[i]:.2f}, Histogram: {histogram[i]:.2f}, Sinyal: {macd_signal}")
        print(f"Bollinger Bantları (Orta: {middle_band[i]:.2f}, Üst: {upper_band[i]:.2f}, Alt: {lower_band[i]:.2f}), Sinyal: {bb_signal}")
        print(f"Stochastic (%K: {k_line[i]:.2f}, %D: {d_line[i]:.2f}), Sinyal: {stoch_signal}")
        print(f"Trend Gücü: {trend_strength[i]:.2f}, Sinyal: {trend_signal}")
        print(f"Toplam Sinyal Gücü: {signal_strength[i]:.2f}, Nihai Sinyal: {signals[i]}")
    
    print("\n--- Piyasa Analizi Tamamlandı ---") # Loglama eklendi
    
    # Sonuçları hazırla
    results = {
        'signals': signals,
        'signal_strength': signal_strength,
        'indicators': {
            'rsi': rsi,
            'macd': macd_line,
            'macd_signal': signal_line,
            'macd_histogram': histogram,
            'bb_middle': middle_band,
            'bb_upper': upper_band,
            'bb_lower': lower_band,
            'stoch_k': k_line,
            'stoch_d': d_line,
            'trend_strength': trend_strength
        }
    }
    
    return results 
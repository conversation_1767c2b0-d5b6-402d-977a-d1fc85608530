"""
Teknik analiz indikatörlerini hesaplayan sınıf
"""
import pandas as pd
import numpy as np
from typing import Dict, Tuple

class TechnicalIndicators:
    def __init__(self):
        """
        TechnicalIndicators sınıfının başlatıcı metodu
        """
        pass

    def calculate_sma(self, data: pd.Series, period: int) -> pd.Series:
        """
        Basit Hareketli Ortalama (SMA) hesaplar
        
        Args:
            data (pd.Series): Fiyat verileri
            period (int): Periyot
            
        Returns:
            pd.Series: Hesaplanan SMA değerleri
        """
        return data.rolling(window=period).mean()

    def calculate_ema(self, data: pd.Series, period: int) -> pd.Series:
        """
        Üssel Hareketli Ortalama (EMA) hesaplar
        
        Args:
            data (pd.Series): Fiyat verileri
            period (int): Periyot
            
        Returns:
            pd.Series: Hesaplanan EMA değerleri
        """
        return data.ewm(span=period, adjust=False).mean()

    def calculate_rsi(self, data: pd.Series, period: int = 14) -> pd.Series:
        """
        Göreceli Güç Endeksi (RSI) hesaplar
        
        Args:
            data (pd.Series): Fiyat verileri
            period (int): Periyot (varsayılan: 14)
            
        Returns:
            pd.Series: Hesaplanan RSI değerleri
        """
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def calculate_macd(self, data: pd.Series, 
                      fast_period: int = 12, 
                      slow_period: int = 26, 
                      signal_period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        MACD (Moving Average Convergence Divergence) hesaplar
        
        Args:
            data (pd.Series): Fiyat verileri
            fast_period (int): Hızlı EMA periyodu (varsayılan: 12)
            slow_period (int): Yavaş EMA periyodu (varsayılan: 26)
            signal_period (int): Sinyal çizgisi periyodu (varsayılan: 9)
            
        Returns:
            Tuple[pd.Series, pd.Series, pd.Series]: (MACD çizgisi, Sinyal çizgisi, Histogram)
        """
        fast_ema = self.calculate_ema(data, fast_period)
        slow_ema = self.calculate_ema(data, slow_period)
        
        macd_line = fast_ema - slow_ema
        signal_line = self.calculate_ema(macd_line, signal_period)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram

    def calculate_bollinger_bands(self, data: pd.Series, 
                                period: int = 20, 
                                std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Bollinger Bantları hesaplar
        
        Args:
            data (pd.Series): Fiyat verileri
            period (int): Periyot (varsayılan: 20)
            std_dev (float): Standart sapma çarpanı (varsayılan: 2.0)
            
        Returns:
            Tuple[pd.Series, pd.Series, pd.Series]: (Orta bant, Üst bant, Alt bant)
        """
        middle_band = self.calculate_sma(data, period)
        std = data.rolling(window=period).std()
        
        upper_band = middle_band + (std * std_dev)
        lower_band = middle_band - (std * std_dev)
        
        return middle_band, upper_band, lower_band

    def add_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Tüm teknik indikatörleri DataFrame'e ekler
        
        Args:
            df (pd.DataFrame): Fiyat verileri içeren DataFrame
            
        Returns:
            pd.DataFrame: İndikatörler eklenmiş DataFrame
        """
        # SMA hesaplamaları
        df['sma_20'] = self.calculate_sma(df['close'], 20)
        df['sma_50'] = self.calculate_sma(df['close'], 50)
        df['sma_200'] = self.calculate_sma(df['close'], 200)
        
        # EMA hesaplamaları
        df['ema_20'] = self.calculate_ema(df['close'], 20)
        df['ema_50'] = self.calculate_ema(df['close'], 50)
        df['ema_200'] = self.calculate_ema(df['close'], 200)
        
        # RSI hesaplaması
        df['rsi'] = self.calculate_rsi(df['close'])
        
        # MACD hesaplaması
        macd_line, signal_line, histogram = self.calculate_macd(df['close'])
        df['macd'] = macd_line
        df['macd_signal'] = signal_line
        df['macd_hist'] = histogram
        
        # Bollinger Bantları hesaplaması
        middle_band, upper_band, lower_band = self.calculate_bollinger_bands(df['close'])
        df['bb_middle'] = middle_band
        df['bb_upper'] = upper_band
        df['bb_lower'] = lower_band
        
        return df 
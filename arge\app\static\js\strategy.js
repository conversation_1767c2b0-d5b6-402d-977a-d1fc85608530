// Strateji ayarları JavaScript dosyası

// Varsayılan strateji değerleri
const defaultStrategy = {
    stopLoss: 4,
    takeProfit: 8,
    trailingStop: 3,
    basePosition: 15,
    maxPosition: 25,
    minPosition: 8,
    rsiLower: 55,
    rsiUpper: 75,
    emaSpread: 0.2,
    momentumScore: 5,
    priceChange3: 0.5,
    priceChange10: 1.0,
    volatilityMax: 4,
    commission: 0.01,
    enableShort: false,
    dynamicTrailing: true
};

// Hazır strateji şablonları
const strategyTemplates = {
    conservative: {
        stopLoss: 2,
        takeProfit: 4,
        trailingStop: 1.5,
        basePosition: 8,
        maxPosition: 15,
        minPosition: 5,
        rsiLower: 45,
        rsiUpper: 70,
        emaSpread: 0.5,
        momentumScore: 6,
        priceChange3: 1.0,
        priceChange10: 2.0,
        volatilityMax: 2,
        commission: 0.01,
        enableShort: false,
        dynamicTrailing: true
    },
    balanced: {
        stopLoss: 3,
        takeProfit: 6,
        trailingStop: 2,
        basePosition: 12,
        maxPosition: 20,
        minPosition: 6,
        rsiLower: 50,
        rsiUpper: 75,
        emaSpread: 0.3,
        momentumScore: 5,
        priceChange3: 0.7,
        priceChange10: 1.5,
        volatilityMax: 3,
        commission: 0.01,
        enableShort: false,
        dynamicTrailing: true
    },
    aggressive: {
        stopLoss: 4,
        takeProfit: 8,
        trailingStop: 3,
        basePosition: 15,
        maxPosition: 25,
        minPosition: 8,
        rsiLower: 55,
        rsiUpper: 75,
        emaSpread: 0.2,
        momentumScore: 5,
        priceChange3: 0.5,
        priceChange10: 1.0,
        volatilityMax: 4,
        commission: 0.01,
        enableShort: false,
        dynamicTrailing: true
    },
    scalping: {
        stopLoss: 1.5,
        takeProfit: 3,
        trailingStop: 1,
        basePosition: 20,
        maxPosition: 35,
        minPosition: 10,
        rsiLower: 60,
        rsiUpper: 80,
        emaSpread: 0.1,
        momentumScore: 4,
        priceChange3: 0.3,
        priceChange10: 0.5,
        volatilityMax: 6,
        commission: 0.01,
        enableShort: true,
        dynamicTrailing: true
    }
};

// Zaman aralığına özel strateji şablonları
const timeframeStrategies = {
    '1m': {
        // 1 dakika için çok konservatif ayarlar
        stopLoss: 1.5,
        takeProfit: 2,
        trailingStop: 1,
        basePosition: 5,
        maxPosition: 10,
        minPosition: 3,
        rsiLower: 40,
        rsiUpper: 60,
        emaSpread: 0.1,
        momentumScore: 6,
        priceChange3: 0.2,
        priceChange10: 0.3,
        volatilityMax: 1.5,
        commission: 0.01,
        enableShort: false,
        dynamicTrailing: true
    },
    '5m': {
        // 5 dakika için konservatif ayarlar
        stopLoss: 2,
        takeProfit: 3,
        trailingStop: 1.5,
        basePosition: 8,
        maxPosition: 15,
        minPosition: 4,
        rsiLower: 45,
        rsiUpper: 70,
        emaSpread: 0.15,
        momentumScore: 5,
        priceChange3: 0.3,
        priceChange10: 0.5,
        volatilityMax: 2,
        commission: 0.01,
        enableShort: false,
        dynamicTrailing: true
    },
    '15m': {
        // 15 dakika için dengeli ayarlar (mevcut agresif)
        stopLoss: 4,
        takeProfit: 8,
        trailingStop: 3,
        basePosition: 15,
        maxPosition: 25,
        minPosition: 8,
        rsiLower: 55,
        rsiUpper: 75,
        emaSpread: 0.2,
        momentumScore: 5,
        priceChange3: 0.5,
        priceChange10: 1.0,
        volatilityMax: 4,
        commission: 0.01,
        enableShort: false,
        dynamicTrailing: true
    },
    '1h': {
        // 1 saat için agresif ayarlar
        stopLoss: 6,
        takeProfit: 12,
        trailingStop: 4,
        basePosition: 20,
        maxPosition: 35,
        minPosition: 10,
        rsiLower: 50,
        rsiUpper: 80,
        emaSpread: 0.3,
        momentumScore: 4,
        priceChange3: 0.8,
        priceChange10: 1.5,
        volatilityMax: 6,
        commission: 0.01,
        enableShort: false,
        dynamicTrailing: true
    },
    '4h': {
        // 4 saat için çok agresif ayarlar
        stopLoss: 8,
        takeProfit: 15,
        trailingStop: 5,
        basePosition: 25,
        maxPosition: 40,
        minPosition: 12,
        rsiLower: 45,
        rsiUpper: 85,
        emaSpread: 0.5,
        momentumScore: 3,
        priceChange3: 1.0,
        priceChange10: 2.0,
        volatilityMax: 8,
        commission: 0.01,
        enableShort: true,
        dynamicTrailing: true
    }
};

// Sayfa yüklendiğinde çalışacak fonksiyonlar
document.addEventListener('DOMContentLoaded', function() {
    initializeSliders();
    loadSavedStrategy();
    updateStrategyProfile();
    
    // Event listener'ları ekle
    document.getElementById('saveStrategy').addEventListener('click', saveStrategy);
    document.getElementById('resetStrategy').addEventListener('click', resetStrategy);
    
    // Slider değişikliklerini dinle
    const sliders = document.querySelectorAll('.form-range');
    sliders.forEach(slider => {
        slider.addEventListener('input', function() {
            updateSliderValue(this);
            updateStrategyProfile();
        });
    });
    
    // Checkbox değişikliklerini dinle
    const checkboxes = document.querySelectorAll('.form-check-input');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateStrategyProfile);
    });
});

// Slider'ları başlat
function initializeSliders() {
    const sliders = [
        'stopLoss', 'takeProfit', 'trailingStop', 'basePosition', 'maxPosition', 'minPosition',
        'rsiLower', 'rsiUpper', 'emaSpread', 'momentumScore', 'priceChange3', 'priceChange10',
        'volatilityMax', 'commission'
    ];
    
    sliders.forEach(sliderId => {
        const slider = document.getElementById(sliderId);
        if (slider) {
            updateSliderValue(slider);
        }
    });
}

// Slider değerini güncelle
function updateSliderValue(slider) {
    const valueSpan = document.getElementById(slider.id + 'Value');
    if (valueSpan) {
        let value = parseFloat(slider.value);
        let displayValue;
        
        if (slider.id === 'commission') {
            displayValue = value.toFixed(2) + '%';
        } else if (slider.id === 'emaSpread' || slider.id === 'priceChange3' || slider.id === 'priceChange10') {
            displayValue = value.toFixed(1) + '%';
        } else if (slider.id === 'momentumScore') {
            displayValue = value + '/6';
        } else {
            displayValue = value + '%';
        }
        
        valueSpan.textContent = displayValue;
    }
}

// Mevcut strateji ayarlarını al
function getCurrentStrategy() {
    return {
        stopLoss: parseFloat(document.getElementById('stopLoss').value),
        takeProfit: parseFloat(document.getElementById('takeProfit').value),
        trailingStop: parseFloat(document.getElementById('trailingStop').value),
        basePosition: parseFloat(document.getElementById('basePosition').value),
        maxPosition: parseFloat(document.getElementById('maxPosition').value),
        minPosition: parseFloat(document.getElementById('minPosition').value),
        rsiLower: parseFloat(document.getElementById('rsiLower').value),
        rsiUpper: parseFloat(document.getElementById('rsiUpper').value),
        emaSpread: parseFloat(document.getElementById('emaSpread').value),
        momentumScore: parseInt(document.getElementById('momentumScore').value),
        priceChange3: parseFloat(document.getElementById('priceChange3').value),
        priceChange10: parseFloat(document.getElementById('priceChange10').value),
        volatilityMax: parseFloat(document.getElementById('volatilityMax').value),
        commission: parseFloat(document.getElementById('commission').value),
        enableShort: document.getElementById('enableShort').checked,
        dynamicTrailing: document.getElementById('dynamicTrailing').checked
    };
}

// Strateji ayarlarını uygula
function applyStrategy(strategy) {
    Object.keys(strategy).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = strategy[key];
            } else {
                element.value = strategy[key];
                updateSliderValue(element);
            }
        }
    });
    updateStrategyProfile();
}

// Hazır strateji yükle
function loadStrategy(templateName) {
    if (strategyTemplates[templateName]) {
        applyStrategy(strategyTemplates[templateName]);

        // Başarı mesajı göster
        showNotification(`${templateName.charAt(0).toUpperCase() + templateName.slice(1)} strateji yüklendi!`, 'success');
    }
}

// Zaman aralığına özel strateji yükle
function loadTimeframeStrategy(timeframe) {
    if (timeframeStrategies[timeframe]) {
        applyStrategy(timeframeStrategies[timeframe]);

        // Başarı mesajı göster
        const timeframeName = {
            '1m': '1 Dakika',
            '5m': '5 Dakika',
            '15m': '15 Dakika',
            '1h': '1 Saat',
            '4h': '4 Saat'
        };

        showNotification(`${timeframeName[timeframe]} için optimize edilmiş strateji yüklendi!`, 'success');
    }
}

// Stratejiyi kaydet
function saveStrategy() {
    const strategy = getCurrentStrategy();
    localStorage.setItem('tradingStrategy', JSON.stringify(strategy));
    
    // API'ye gönder
    fetch('/api/save-strategy', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(strategy)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Strateji başarıyla kaydedildi!', 'success');
        } else {
            showNotification('Strateji kaydedilirken hata oluştu!', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Strateji kaydedilirken hata oluştu!', 'error');
    });
}

// Kaydedilmiş stratejiyi yükle
function loadSavedStrategy() {
    const saved = localStorage.getItem('tradingStrategy');
    if (saved) {
        try {
            const strategy = JSON.parse(saved);
            applyStrategy(strategy);
        } catch (error) {
            console.error('Saved strategy parse error:', error);
            applyStrategy(defaultStrategy);
        }
    } else {
        applyStrategy(defaultStrategy);
    }
}

// Varsayılan ayarlara sıfırla
function resetStrategy() {
    if (confirm('Tüm ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?')) {
        applyStrategy(defaultStrategy);
        showNotification('Strateji varsayılan ayarlara sıfırlandı!', 'info');
    }
}

// Strateji profilini güncelle
function updateStrategyProfile() {
    const strategy = getCurrentStrategy();
    
    // Risk skoru hesapla (0-100)
    const riskScore = calculateRiskScore(strategy);
    
    // Profil belirle
    let profileName, profileDescription, profileClass;
    if (riskScore < 25) {
        profileName = 'Konservatif';
        profileDescription = 'Düşük risk, istikrarlı kar';
        profileClass = 'bg-success';
    } else if (riskScore < 50) {
        profileName = 'Dengeli';
        profileDescription = 'Orta risk, orta kar';
        profileClass = 'bg-primary';
    } else if (riskScore < 75) {
        profileName = 'Agresif';
        profileDescription = 'Yüksek risk, yüksek kar';
        profileClass = 'bg-warning';
    } else {
        profileName = 'Çok Agresif';
        profileDescription = 'Çok yüksek risk, çok yüksek kar';
        profileClass = 'bg-danger';
    }
    
    // UI'ı güncelle
    document.getElementById('profileName').textContent = profileName;
    document.getElementById('profileName').className = `badge ${profileClass}`;
    document.getElementById('profileDescription').textContent = profileDescription;
    
    // Beklenen özellikleri güncelle
    updateExpectedFeatures(strategy, riskScore);
}

// Risk skoru hesapla
function calculateRiskScore(strategy) {
    let score = 0;
    
    // Pozisyon büyüklüğü (0-30 puan)
    score += (strategy.basePosition / 30) * 30;
    
    // Stop loss (ters orantılı, 0-20 puan)
    score += (10 - strategy.stopLoss) / 10 * 20;
    
    // Take profit (0-15 puan)
    score += (strategy.takeProfit / 15) * 15;
    
    // Momentum score (ters orantılı, 0-15 puan)
    score += (6 - strategy.momentumScore) / 6 * 15;
    
    // Volatilite toleransı (0-10 puan)
    score += (strategy.volatilityMax / 10) * 10;
    
    // Short pozisyonlar (0-10 puan)
    if (strategy.enableShort) score += 10;
    
    return Math.min(score, 100);
}

// Beklenen özellikleri güncelle
function updateExpectedFeatures(strategy, riskScore) {
    const features = {
        trades: riskScore < 50 ? 'Az işlem sayısı' : 'Çok işlem sayısı',
        winRate: riskScore < 30 ? 'Yüksek kazanma oranı' : riskScore < 70 ? 'Orta kazanma oranı' : 'Düşük kazanma oranı',
        profit: riskScore < 25 ? 'Düşük kar potansiyeli' : riskScore < 75 ? 'Orta kar potansiyeli' : 'Yüksek kar potansiyeli',
        risk: riskScore < 25 ? 'Düşük risk' : riskScore < 50 ? 'Orta risk' : riskScore < 75 ? 'Yüksek risk' : 'Çok yüksek risk'
    };
    
    document.getElementById('expectedTrades').textContent = features.trades;
    document.getElementById('expectedWinRate').textContent = features.winRate;
    document.getElementById('expectedProfit').textContent = features.profit;
    document.getElementById('expectedRisk').textContent = features.risk;
}

// Bildirim göster
function showNotification(message, type) {
    // Bootstrap toast veya basit alert kullan
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3 saniye sonra otomatik kapat
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

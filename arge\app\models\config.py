"""
Konfigürasyon sınıfı
"""
from typing import Dict, Any
import json
import os

class Config:
    def __init__(self, config_file: str = 'config.json'):
        """
        Config sınıfının başlatıcı metodu
        
        Args:
            config_file (str): Konfigürasyon dosyasının yolu
        """
        self.config_file = config_file
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        Konfigürasyon dosyasını yükler
        
        Returns:
            Dict[str, Any]: Konfigürasyon verileri
        """
        default_config = {
            'LEVERAGE': 1,  # Kaldıraç oranı
            'INITIAL_BALANCE': 10000,  # Başlangıç bakiyesi
            'DEFAULT_SYMBOL': 'BTC/USDT',  # Varsayılan sembol
            'DEFAULT_TIMEFRAME': '1h',  # Varsay<PERSON>lan zaman dilimi
            'DEFAULT_START_DATE': '2023-01-01',  # Varsayılan başlangıç tarihi
            'RISK_FREE_RATE': 0.02,  # Risksiz faiz oranı
            'INDICATOR_PARAMS': {  # İndikatör parametreleri
                'RSI_PERIOD': 14,
                'RSI_OVERBOUGHT': 70,  # Daha yüksek aşırı alım seviyesi (daha seçici)
                'RSI_OVERSOLD': 30,    # Daha düşük aşırı satım seviyesi (daha seçici)
                'EMA_FAST': 10,        # Daha kısa hızlı EMA
                'EMA_SLOW': 30,        # Daha kısa yavaş EMA
                'MACD_FAST': 8,        # Daha kısa MACD hızlı EMA
                'MACD_SLOW': 21,       # Daha kısa MACD yavaş EMA
                'MACD_SIGNAL': 5,      # Daha kısa MACD sinyal çizgisi
                'BB_PERIOD': 14,       # Daha kısa BB periyodu
                'BB_STD': 2.0          # Standart BB bantları (daha seçici)
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                print(f"Konfigürasyon dosyası okuma hatası: {e}")
        
        # Load config values as instance attributes
        for key, value in default_config.items():
            setattr(self, key, value)

        return default_config # Although we set attributes, still return the dict for completeness
    
    def save_config(self) -> None:
        """
        Konfigürasyonu dosyaya kaydeder
        """
        try:
            # Save attributes back to the config dictionary before saving to file
            for key in default_config.keys(): # Use keys from default_config to avoid saving unexpected attributes
                if hasattr(self, key):
                    default_config[key] = getattr(self, key)

            with open(self.config_file, 'w') as f:
                json.dump(default_config, f, indent=4)
        except Exception as e:
            print(f"Konfigürasyon dosyası yazma hatası: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Konfigürasyon değerini döndürür
        
        Args:
            key (str): Konfigürasyon anahtarı
            default (Any): Varsayılan değer
            
        Returns:
            Any: Konfigürasyon değeri
        """
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """
        Konfigürasyon değerini günceller
        
        Args:
            key (str): Konfigürasyon anahtarı
            value (Any): Yeni değer
        """
        self.config[key] = value
        self.save_config()
    
    def update(self, config_dict: Dict[str, Any]) -> None:
        """
        Konfigürasyonu günceller
        
        Args:
            config_dict (Dict[str, Any]): Yeni konfigürasyon verileri
        """
        self.config.update(config_dict)
        self.save_config()
    
    def get_indicator_params(self) -> Dict[str, Any]:
        """
        İndikatör parametrelerini döndürür
        
        Returns:
            Dict[str, Any]: İndikatör parametreleri
        """
        return self.config.get('INDICATOR_PARAMS', {})
    
    def set_indicator_params(self, params: Dict[str, Any]) -> None:
        """
        İndikatör parametrelerini günceller
        
        Args:
            params (Dict[str, Any]): Yeni indikatör parametreleri
        """
        self.config['INDICATOR_PARAMS'] = params
        self.save_config() 
"""
Birleştirilmiş konfigürasyon sınıfı - Flask ve uygulama ayarları
"""
from typing import Dict, Any
import json
import os

class Config:
    """Flask ve uygulama konfigürasyonu"""

    # Flask ayarları
    SECRET_KEY = 'gizli-anahtar-buraya'
    DEBUG = True

    # Binance API ayarları
    BINANCE_API_URL = 'https://api.binance.com/api/v3'

    # Veri çekme ayarları
    DEFAULT_SYMBOL = 'BTCUSDT'
    DEFAULT_INTERVAL = '1h'
    DEFAULT_LIMIT = 1000

    # Simülasyon ayarları
    INITIAL_BALANCE = 10000
    LEVERAGE = 1
    COMMISSION_RATE = 0.001
    TAKE_PROFIT_PERCENTAGE = 5
    STOP_LOSS_PERCENTAGE = 10

    def __init__(self, config_file: str = 'config.json'):
        """
        Config sınıfının başlatıcı metodu

        Args:
            config_file (str): Konfigürasyon dosyasının yolu
        """
        self.config_file = config_file
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """
        Konfigürasyon dosyasını yükler

        Returns:
            Dict[str, Any]: Konfigürasyon verileri
        """
        default_config = {
            'LEVERAGE': self.LEVERAGE,
            'INITIAL_BALANCE': self.INITIAL_BALANCE,
            'DEFAULT_SYMBOL': 'BTC/USDT',
            'DEFAULT_TIMEFRAME': '1h',
            'DEFAULT_START_DATE': '2023-01-01',
            'RISK_FREE_RATE': 0.02,
            'COMMISSION_RATE': self.COMMISSION_RATE,
            'TAKE_PROFIT_PERCENTAGE': self.TAKE_PROFIT_PERCENTAGE,
            'STOP_LOSS_PERCENTAGE': self.STOP_LOSS_PERCENTAGE,
            'INDICATOR_PARAMS': {
                'RSI_PERIOD': 14,
                'RSI_OVERBOUGHT': 70,
                'RSI_OVERSOLD': 30,
                'EMA_FAST': 10,
                'EMA_SLOW': 30,
                'MACD_FAST': 8,
                'MACD_SLOW': 21,
                'MACD_SIGNAL': 5,
                'BB_PERIOD': 14,
                'BB_STD': 2.0
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                print(f"Konfigürasyon dosyası okuma hatası: {e}")
        
        # Load config values as instance attributes
        for key, value in default_config.items():
            setattr(self, key, value)

        return default_config # Although we set attributes, still return the dict for completeness
    
    def save_config(self) -> None:
        """
        Konfigürasyonu dosyaya kaydeder
        """
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            print(f"Konfigürasyon dosyası yazma hatası: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Konfigürasyon değerini döndürür
        
        Args:
            key (str): Konfigürasyon anahtarı
            default (Any): Varsayılan değer
            
        Returns:
            Any: Konfigürasyon değeri
        """
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """
        Konfigürasyon değerini günceller
        
        Args:
            key (str): Konfigürasyon anahtarı
            value (Any): Yeni değer
        """
        self.config[key] = value
        self.save_config()
    
    def update(self, config_dict: Dict[str, Any]) -> None:
        """
        Konfigürasyonu günceller
        
        Args:
            config_dict (Dict[str, Any]): Yeni konfigürasyon verileri
        """
        self.config.update(config_dict)
        self.save_config()
    
    def get_indicator_params(self) -> Dict[str, Any]:
        """
        İndikatör parametrelerini döndürür
        
        Returns:
            Dict[str, Any]: İndikatör parametreleri
        """
        return self.config.get('INDICATOR_PARAMS', {})
    
    def set_indicator_params(self, params: Dict[str, Any]) -> None:
        """
        İndikatör parametrelerini günceller
        
        Args:
            params (Dict[str, Any]): Yeni indikatör parametreleri
        """
        self.config['INDICATOR_PARAMS'] = params
        self.save_config() 